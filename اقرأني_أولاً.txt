═══════════════════════════════════════════════════════════════
                برنامج شعبة الأحياء - قسم الطبابة العدلية
                         دليل التشغيل السريع
═══════════════════════════════════════════════════════════════

🚀 للتشغيل الفوري:

   Windows:
   ========
   اضغط مرتين على: RUN.bat
   
   أو افتح Command Prompt واكتب:
   python START.py

   Linux/Mac:
   ==========
   افتح Terminal واكتب:
   python3 START.py

───────────────────────────────────────────────────────────────

❌ إذا لم يعمل البرنامج:

   1. تأكد من تثبيت Python:
      - تحميل من: https://python.org
      - اختر "Add Python to PATH" أثناء التثبيت
      - إعادة تشغيل الكمبيوتر بعد التثبيت

   2. افتح Command Prompt واكتب:
      python --version
      (يجب أن يظهر رقم الإصدار)

   3. إذا ظهر خطأ، جرب:
      py --version
      أو
      python3 --version

───────────────────────────────────────────────────────────────

📋 خطوات التشغيل المفصلة:

   1. شغل: python START.py
   2. اختر "1" لاختبار النظام
   3. إذا فشل، اختر "2" لتثبيت المكتبات
   4. ثم اختر "3" لتشغيل الخادم
   5. افتح المتصفح على: http://localhost:8000

───────────────────────────────────────────────────────────────

📁 الملفات المهمة:

   RUN.bat              - تشغيل سريع (Windows)
   START.py             - القائمة الرئيسية
   test_basic.py        - اختبار النظام
   install_simple.py    - تثبيت المكتبات
   run_basic.py         - تشغيل الخادم

───────────────────────────────────────────────────────────────

🔗 روابط مفيدة:

   - تحميل Python: https://python.org/downloads/
   - دليل pip: https://pip.pypa.io/en/stable/
   - مساعدة Python: https://docs.python.org/

───────────────────────────────────────────────────────────────

💡 نصائح:

   - ابدأ دائماً بـ START.py
   - إذا واجهت مشاكل، شارك نتائج test_basic.py
   - تأكد من الاتصال بالإنترنت عند تثبيت المكتبات
   - استخدم Command Prompt كمدير إذا فشل التثبيت

═══════════════════════════════════════════════════════════════
