import React, { useState } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Input, 
  Modal, 
  Form, 
  DatePicker, 
  Select,
  Typography,
  Popconfirm,
  message,
  Tag,
  Tooltip
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  FileTextOutlined,
  DownloadOutlined,
  UserAddOutlined,
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import dayjs from 'dayjs'
import { ageEstimationAPI, reportsAPI } from '../services/api'
import AgeEstimationForm from '../components/Forms/AgeEstimationForm'

const { Title } = Typography
const { Search } = Input
const { Option } = Select

const AgeEstimation = () => {
  const [searchText, setSearchText] = useState('')
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingCase, setEditingCase] = useState(null)
  const [form] = Form.useForm()
  const queryClient = useQueryClient()

  // جلب البيانات
  const { data: cases, isLoading } = useQuery(
    ['age-estimation-cases', searchText],
    () => ageEstimationAPI.getAll({ search: searchText }),
    {
      keepPreviousData: true,
    }
  )

  // حذف حالة
  const deleteMutation = useMutation(ageEstimationAPI.delete, {
    onSuccess: () => {
      message.success('تم حذف الحالة بنجاح')
      queryClient.invalidateQueries('age-estimation-cases')
    },
    onError: () => {
      message.error('خطأ في حذف الحالة')
    },
  })

  // تحميل التقرير المفرد
  const downloadIndividualReport = async (caseId, technicalNumber) => {
    try {
      const response = await reportsAPI.generateIndividualReport(caseId)
      
      // إنشاء رابط التحميل
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `تقرير_مفرد_${technicalNumber}.docx`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      message.success('تم تحميل التقرير بنجاح')
    } catch (error) {
      message.error('خطأ في تحميل التقرير')
    }
  }

  // أعمدة الجدول
  const columns = [
    {
      title: 'الرقم الفني',
      dataIndex: 'technical_number',
      key: 'technical_number',
      width: 120,
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: 'اسم رب الأسرة',
      dataIndex: 'family_head_name',
      key: 'family_head_name',
      width: 150,
    },
    {
      title: 'الجهة المرسلة',
      dataIndex: 'sending_authority',
      key: 'sending_authority',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: 'تاريخ اجتماع اللجنة',
      dataIndex: 'committee_meeting_date',
      key: 'committee_meeting_date',
      width: 150,
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: 'عدد الأطفال',
      dataIndex: 'children',
      key: 'children_count',
      width: 100,
      align: 'center',
      render: (children) => (
        <Tag color="green">{children?.length || 0}</Tag>
      ),
    },
    {
      title: 'تاريخ الإنشاء',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 200,
      fixed: 'left',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="تعديل">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          <Tooltip title="تحميل التقرير المفرد">
            <Button
              type="default"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => downloadIndividualReport(record.id, record.technical_number)}
            />
          </Tooltip>
          
          <Popconfirm
            title="هل أنت متأكد من حذف هذه الحالة؟"
            onConfirm={() => deleteMutation.mutate(record.id)}
            okText="نعم"
            cancelText="لا"
          >
            <Tooltip title="حذف">
              <Button
                type="primary"
                danger
                size="small"
                icon={<DeleteOutlined />}
                loading={deleteMutation.isLoading}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const handleEdit = (record) => {
    setEditingCase(record)
    setIsModalVisible(true)
  }

  const handleAdd = () => {
    setEditingCase(null)
    setIsModalVisible(true)
  }

  const handleModalClose = () => {
    setIsModalVisible(false)
    setEditingCase(null)
    form.resetFields()
  }

  const handleFormSuccess = () => {
    handleModalClose()
    queryClient.invalidateQueries('age-estimation-cases')
  }

  return (
    <div>
      {/* العنوان والأدوات */}
      <Card className="neumorphic-card" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            <UserAddOutlined style={{ marginLeft: 8 }} />
            تقدير الأعمار
          </Title>
          
          <Space>
            <Search
              placeholder="البحث في الحالات..."
              allowClear
              style={{ width: 300 }}
              onSearch={setSearchText}
              onChange={(e) => !e.target.value && setSearchText('')}
            />
            
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              className="neumorphic-button"
            >
              إضافة حالة جديدة
            </Button>
          </Space>
        </div>
      </Card>

      {/* جدول البيانات */}
      <Card className="neumorphic-card">
        <Table
          columns={columns}
          dataSource={cases?.data || []}
          loading={isLoading}
          rowKey="id"
          scroll={{ x: 1200 }}
          pagination={{
            total: cases?.data?.length || 0,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} من ${total} حالة`,
          }}
          locale={{
            emptyText: 'لا توجد حالات',
          }}
        />
      </Card>

      {/* نموذج إضافة/تعديل */}
      <Modal
        title={editingCase ? 'تعديل حالة تقدير الأعمار' : 'إضافة حالة تقدير الأعمار جديدة'}
        open={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        destroyOnClose
      >
        <AgeEstimationForm
          initialValues={editingCase}
          onSuccess={handleFormSuccess}
          onCancel={handleModalClose}
        />
      </Modal>
    </div>
  )
}

export default AgeEstimation
