Write-Host "Forensic Biology Program Starting..." -ForegroundColor Green
Write-Host ""

# Try different Python commands
$pythonCommands = @("python", "py", "py.exe", "python.exe")

foreach ($cmd in $pythonCommands) {
    try {
        Write-Host "Trying: $cmd" -ForegroundColor Yellow
        & $cmd --version
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Success! Starting server with: $cmd" -ForegroundColor Green
            & $cmd server.py
            break
        }
    }
    catch {
        Write-Host "Failed: $cmd not found" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
