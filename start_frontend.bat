@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    واجهة شعبة الأحياء الأمامية
echo ========================================
echo.

echo 🔧 التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js من https://nodejs.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Node.js

echo.
echo 📦 تثبيت المتطلبات...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo.
echo 🚀 بدء تشغيل الواجهة الأمامية...
echo 🌐 الواجهة متاحة على: http://localhost:3000
echo 🛑 اضغط Ctrl+C لإيقاف الخادم
echo.

npm run dev

pause
