"""
سكريبت إنشاء مستخدم مدير افتراضي
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from database import SessionLocal, init_db
from models import User
from routes.auth import get_password_hash

def create_admin_user():
    """إنشاء مستخدم مدير افتراضي"""

    try:
        # إنشاء قاعدة البيانات
        init_db()

        # إنشاء جلسة
        db = SessionLocal()

        try:
            # التحقق من وجود مدير
            existing_admin = db.query(User).filter(User.role == "admin").first()

            if existing_admin:
                print("✅ يوجد مدير بالفعل في النظام")
                print(f"   اسم المستخدم: {existing_admin.username}")
                return

            # إنشاء مستخدم مدير افتراضي
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                full_name="مدير النظام",
                role="admin",
                is_active=True
            )

            db.add(admin_user)
            db.commit()

            print("✅ تم إنشاء مستخدم المدير الافتراضي بنجاح!")
            print("   اسم المستخدم: admin")
            print("   كلمة المرور: admin123")
            print("   ⚠️  يرجى تغيير كلمة المرور بعد تسجيل الدخول")

            # إنشاء مستخدم عادي للاختبار
            test_user = User(
                username="user",
                email="<EMAIL>",
                hashed_password=get_password_hash("user123"),
                full_name="مستخدم تجريبي",
                role="user",
                is_active=True
            )

            db.add(test_user)
            db.commit()

            print("✅ تم إنشاء مستخدم تجريبي:")
            print("   اسم المستخدم: user")
            print("   كلمة المرور: user123")

        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدمين: {e}")
            db.rollback()
        finally:
            db.close()

    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")

if __name__ == "__main__":
    print("🔧 إنشاء مستخدمين افتراضيين...")
    create_admin_user()
    print("🎉 تم الانتهاء!")
