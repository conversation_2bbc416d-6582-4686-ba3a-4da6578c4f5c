"""
إعداد قاعدة البيانات لبرنامج شعبة الأحياء
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

try:
    from config import DATABASE_URL, DATA_DIR
    # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
    DATA_DIR.mkdir(exist_ok=True)
    SQLALCHEMY_DATABASE_URL = DATABASE_URL
except ImportError:
    # إعداد افتراضي إذا لم يكن ملف config متوفراً
    os.makedirs("data", exist_ok=True)
    SQLALCHEMY_DATABASE_URL = "sqlite:///./data/forensic_biology.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, 
    connect_args={"check_same_thread": False},
    echo=True  # لعرض استعلامات SQL في وضع التطوير
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    """
    دالة للحصول على جلسة قاعدة البيانات
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """
    إنشاء جداول قاعدة البيانات
    """
    Base.metadata.create_all(bind=engine)
    print("تم إنشاء قاعدة البيانات بنجاح")

def reset_db():
    """
    إعادة تعيين قاعدة البيانات (حذف وإعادة إنشاء)
    """
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    print("تم إعادة تعيين قاعدة البيانات بنجاح")
