# برنامج قواعد بيانات شعبة الأحياء - قسم الطبابة العدلية

## 📋 نظرة عامة
برنامج شامل لإدارة بيانات شعبة الأحياء في قسم الطبابة العدلية، يشمل:
- تقدير الأعمار
- فحص الشدة
- تحديد الجنس
- الوقوعات الجنسية
- التقارير والإحصائيات
- إدارة المستخدمين

## 🛠️ التقنيات المستخدمة
- **Backend**: Python + FastAPI
- **Database**: SQLite
- **Frontend**: React + Vite + Ant Design
- **Desktop**: Electron
- **Reports**: Python-docx للتقارير Word

## 📁 هيكل المشروع
```
شعبة الاحياء/
├── backend/                 # خادم API
│   ├── main.py             # الخادم الرئيسي
│   ├── models.py           # نماذج قاعدة البيانات
│   ├── database.py         # إعداد قاعدة البيانات
│   ├── routes/             # مسارات API
│   ├── services/           # خدمات العمل
│   └── templates/          # قوالب التقارير
├── frontend/               # واجهة الويب
│   ├── src/
│   │   ├── components/     # مكونات الواجهة
│   │   ├── pages/          # صفحات التطبيق
│   │   └── services/       # خدمات API
│   └── package.json
├── desktop/                # تطبيق سطح المكتب
│   ├── main.js
│   └── package.json
└── requirements.txt        # متطلبات Python
```

## 🚀 التشغيل السريع

### ⭐ الطريقة الأسهل - ابدأ هنا!

#### Windows:
```bash
# اضغط مرتين على الملف
RUN.bat

# أو افتح Command Prompt واكتب
python START.py
```

#### Linux/Mac:
```bash
python3 START.py
```

### 📋 خطوات التشغيل:
1. شغل `python START.py`
2. اختر "1" لاختبار النظام
3. إذا فشل، اختر "2" لتثبيت المكتبات
4. ثم اختر "3" لتشغيل الخادم
5. افتح المتصفح على: http://localhost:8000

### 🔧 إذا واجهت مشاكل
```bash
# 1. تشخيص المشاكل
python diagnose.py

# 2. تثبيت المتطلبات
python install_requirements.py

# 3. تشغيل بسيط
python simple_start.py
```

### 📋 طرق التشغيل المختلفة

#### 1. تشغيل بسيط (بدون قاعدة بيانات)
```bash
python simple_start.py
```

#### 2. تشغيل سريع (مع قاعدة البيانات)
```bash
python quick_start.py
```

#### 3. تشغيل كامل (جميع الميزات)
```bash
python run_full_system.py
```

#### 4. Windows - ملفات bat
```bash
quick_start.bat     # تشغيل سريع
start.bat          # تشغيل كامل
```

### 🔗 روابط الوصول
- **API الخلفي**: http://localhost:8000
- **وثائق API**: http://localhost:8000/docs
- **معلومات النظام**: http://localhost:8000/info
- **الواجهة الأمامية**: http://localhost:3000 (إذا تم تشغيلها)

### 👤 بيانات تسجيل الدخول الافتراضية
- **المدير**: `admin` / `admin123`
- **المستخدم**: `user` / `user123`

### 📋 المتطلبات
- **Python 3.7+** (مطلوب)
- **Node.js 16+** (للواجهة الأمامية فقط)

## 🆘 استكشاف الأخطاء

إذا لم يعمل البرنامج:

1. **ابدأ بالتشخيص**: `python diagnose.py`
2. **اقرأ دليل الأخطاء**: `استكشاف_الأخطاء.md`
3. **جرب التشغيل البسيط**: `python simple_start.py`

### مشاكل شائعة:
- **Python غير موجود**: تحميل من https://python.org
- **pip غير موجود**: `python -m ensurepip --upgrade`
- **فشل تثبيت المتطلبات**: `python install_requirements.py`
- **المنفذ مستخدم**: تغيير المنفذ أو إيقاف العملية الأخرى

## 📊 الميزات الرئيسية

### 1. تقدير الأعمار
- إدخال بيانات الأطفال
- توليد تقارير مفردة وموحدة
- حفظ البيانات في قاعدة البيانات

### 2. فحص الشدة
- تسجيل حالات فحص الشدة
- إدارة البيانات الطبية

### 3. تحديد الجنس
- فحوصات تحديد الجنس
- التوثيق الطبي

### 4. الوقوعات الجنسية
- تسجيل الحالات
- إدارة البيانات الحساسة

### 5. التقارير والإحصائيات
- تقارير شهرية وسنوية
- إحصائيات مفصلة
- تصدير البيانات

### 6. إدارة المستخدمين
- نظام تسجيل دخول
- صلاحيات متعددة المستويات
- سجل العمليات

## 🔒 الأمان
- تشفير كلمات المرور
- صلاحيات المستخدمين
- تسجيل العمليات
- نسخ احتياطية آمنة

## 📱 الواجهة
- تصميم نيومورفيك عصري
- دعم اللغة العربية (RTL)
- واجهة متجاوبة
- سهولة الاستخدام

## 🔧 الإعدادات
- إدارة القوالب
- النسخ الاحتياطي
- إعدادات الشبكة
- تخصيص التقارير
