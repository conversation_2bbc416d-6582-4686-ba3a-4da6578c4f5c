import React, { createContext, useContext, useState, useEffect } from 'react'
import { message } from 'antd'
import api from '../services/api'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [token, setToken] = useState(localStorage.getItem('token'))

  // تحديث رأس التفويض في API
  useEffect(() => {
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`
      // التحقق من صحة التوكن
      checkAuth()
    } else {
      delete api.defaults.headers.common['Authorization']
      setLoading(false)
    }
  }, [token])

  const checkAuth = async () => {
    try {
      const response = await api.get('/auth/me')
      setUser(response.data)
    } catch (error) {
      console.error('خطأ في التحقق من المصادقة:', error)
      logout()
    } finally {
      setLoading(false)
    }
  }

  const login = async (credentials) => {
    try {
      setLoading(true)
      const response = await api.post('/auth/login', credentials)
      const { access_token, user_info } = response.data
      
      // حفظ التوكن
      localStorage.setItem('token', access_token)
      setToken(access_token)
      setUser(user_info)
      
      message.success('تم تسجيل الدخول بنجاح')
      return { success: true }
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'خطأ في تسجيل الدخول'
      message.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  const register = async (userData) => {
    try {
      setLoading(true)
      const response = await api.post('/auth/register', userData)
      message.success('تم إنشاء الحساب بنجاح')
      return { success: true, data: response.data }
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'خطأ في إنشاء الحساب'
      message.error(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setLoading(false)
    }
  }

  const logout = () => {
    localStorage.removeItem('token')
    setToken(null)
    setUser(null)
    delete api.defaults.headers.common['Authorization']
    message.info('تم تسجيل الخروج')
  }

  const updateUser = (userData) => {
    setUser(prev => ({ ...prev, ...userData }))
  }

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    updateUser,
    isAuthenticated: !!user,
    isAdmin: user?.role === 'admin',
    isManager: user?.role === 'manager' || user?.role === 'admin'
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
