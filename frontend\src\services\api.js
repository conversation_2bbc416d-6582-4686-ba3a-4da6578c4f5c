import axios from 'axios'
import { message } from 'antd'

// إنشاء مثيل axios
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// معترض الطلبات
api.interceptors.request.use(
  (config) => {
    // إضافة التوكن إلى الطلبات
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    console.log('📤 طلب API:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('❌ خطأ في الطلب:', error)
    return Promise.reject(error)
  }
)

// معترض الاستجابات
api.interceptors.response.use(
  (response) => {
    console.log('📥 استجابة API:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('❌ خطأ في الاستجابة:', error)
    
    // معالجة الأخطاء الشائعة
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // غير مصرح
          localStorage.removeItem('token')
          window.location.href = '/login'
          message.error('انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى')
          break
          
        case 403:
          // ممنوع
          message.error('ليس لديك صلاحية للوصول إلى هذا المورد')
          break
          
        case 404:
          // غير موجود
          message.error('المورد المطلوب غير موجود')
          break
          
        case 422:
          // خطأ في التحقق
          const validationErrors = data.detail
          if (Array.isArray(validationErrors)) {
            validationErrors.forEach(err => {
              message.error(`${err.loc?.join(' → ')}: ${err.msg}`)
            })
          } else {
            message.error(data.detail || 'خطأ في البيانات المدخلة')
          }
          break
          
        case 500:
          // خطأ في الخادم
          message.error('خطأ في الخادم، يرجى المحاولة لاحقاً')
          break
          
        default:
          message.error(data.detail || 'حدث خطأ غير متوقع')
      }
    } else if (error.request) {
      // لا توجد استجابة من الخادم
      message.error('لا يمكن الاتصال بالخادم، تحقق من الاتصال بالإنترنت')
    } else {
      // خطأ في إعداد الطلب
      message.error('خطأ في إعداد الطلب')
    }
    
    return Promise.reject(error)
  }
)

// دوال API مخصصة

// المصادقة
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getMe: () => api.get('/auth/me'),
  logout: () => api.post('/auth/logout'),
}

// تقدير الأعمار
export const ageEstimationAPI = {
  getAll: (params) => api.get('/age-estimation', { params }),
  getById: (id) => api.get(`/age-estimation/${id}`),
  create: (data) => api.post('/age-estimation', data),
  update: (id, data) => api.put(`/age-estimation/${id}`, data),
  delete: (id) => api.delete(`/age-estimation/${id}`),
  addChild: (caseId, childData) => api.post(`/age-estimation/${caseId}/children`, childData),
  deleteChild: (childId) => api.delete(`/age-estimation/children/${childId}`),
}

// فحص الشدة
export const intensityExamAPI = {
  getAll: (params) => api.get('/intensity-exam', { params }),
  getById: (id) => api.get(`/intensity-exam/${id}`),
  create: (data) => api.post('/intensity-exam', data),
  update: (id, data) => api.put(`/intensity-exam/${id}`, data),
  delete: (id) => api.delete(`/intensity-exam/${id}`),
}

// تحديد الجنس
export const genderDeterminationAPI = {
  getAll: (params) => api.get('/gender-determination', { params }),
  getById: (id) => api.get(`/gender-determination/${id}`),
  create: (data) => api.post('/gender-determination', data),
  update: (id, data) => api.put(`/gender-determination/${id}`, data),
  delete: (id) => api.delete(`/gender-determination/${id}`),
}

// الوقوعات الجنسية
export const sexualAssaultAPI = {
  getAll: (params) => api.get('/sexual-assault', { params }),
  getById: (id) => api.get(`/sexual-assault/${id}`),
  create: (data) => api.post('/sexual-assault', data),
  update: (id, data) => api.put(`/sexual-assault/${id}`, data),
  delete: (id) => api.delete(`/sexual-assault/${id}`),
}

// التقارير
export const reportsAPI = {
  getStats: (params) => api.get('/reports/stats', { params }),
  generateIndividualReport: (caseId) => api.get(`/reports/age-estimation/individual/${caseId}`, {
    responseType: 'blob'
  }),
  generateUnifiedReport: (date) => api.get('/reports/age-estimation/unified', {
    params: { committee_date: date },
    responseType: 'blob'
  }),
}

// إدارة المستخدمين
export const usersAPI = {
  getAll: (params) => api.get('/users', { params }),
  getById: (id) => api.get(`/users/${id}`),
  update: (id, data) => api.put(`/users/${id}`, data),
  delete: (id) => api.delete(`/users/${id}`),
  getActivityLogs: (params) => api.get('/users/activity-logs', { params }),
}

// الإعدادات
export const settingsAPI = {
  getAll: () => api.get('/settings'),
  create: (data) => api.post('/settings', data),
  update: (id, data) => api.put(`/settings/${id}`, data),
  delete: (id) => api.delete(`/settings/${id}`),
  createBackup: () => api.post('/settings/backup'),
  restoreBackup: (file) => {
    const formData = new FormData()
    formData.append('backup_file', file)
    return api.post('/settings/restore', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  getBackups: () => api.get('/settings/backups'),
}

export default api
