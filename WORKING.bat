@echo off
echo Forensic Biology Program Starting...
echo.

REM Try the full path to python
where python >nul 2>&1
if %errorlevel% equ 0 (
    echo Found python - starting server...
    python server.py
    goto end
)

REM Try py launcher without arguments
py -3 server.py 2>nul
if %errorlevel% equ 0 goto end

REM Try direct python call
C:\Python39\python.exe server.py 2>nul
if %errorlevel% equ 0 goto end

C:\Python310\python.exe server.py 2>nul
if %errorlevel% equ 0 goto end

C:\Python311\python.exe server.py 2>nul
if %errorlevel% equ 0 goto end

C:\Python312\python.exe server.py 2>nul
if %errorlevel% equ 0 goto end

echo ERROR: Could not find working Python installation
echo.
echo Please try one of these solutions:
echo 1. Install Python from https://python.org
echo 2. Make sure to check "Add Python to PATH"
echo 3. Restart your computer after installation
echo.

:end
pause
