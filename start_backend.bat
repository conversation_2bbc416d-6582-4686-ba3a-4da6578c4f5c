@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    خادم شعبة الأحياء الخلفي
echo ========================================
echo.

echo 🔧 التحقق من Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python 3.8+ من https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

echo.
echo 📦 تثبيت المتطلبات...
cd backend
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo.
echo 👤 إنشاء المستخدمين الافتراضيين...
python create_admin.py

echo.
echo 🚀 بدء تشغيل الخادم الخلفي...
echo 🌐 الخادم متاح على: http://localhost:8000
echo 📚 وثائق API: http://localhost:8000/api/docs
echo 🛑 اضغط Ctrl+C لإيقاف الخادم
echo.

python run_server.py

pause
