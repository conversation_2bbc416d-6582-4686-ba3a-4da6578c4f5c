import React, { useState } from 'react'
import { Card, Table, Button, Space, Input, Modal, Typography, message } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, ExperimentOutlined } from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import dayjs from 'dayjs'
import { intensityExamAPI } from '../services/api'

const { Title } = Typography
const { Search } = Input

const IntensityExam = () => {
  const [searchText, setSearchText] = useState('')
  const [isModalVisible, setIsModalVisible] = useState(false)
  const queryClient = useQueryClient()

  const { data: cases, isLoading } = useQuery(
    ['intensity-exam-cases', searchText],
    () => intensityExamAPI.getAll({ search: searchText })
  )

  const deleteMutation = useMutation(intensityExamAPI.delete, {
    onSuccess: () => {
      message.success('تم حذف الحالة بنجاح')
      queryClient.invalidateQueries('intensity-exam-cases')
    },
  })

  const columns = [
    {
      title: 'الرقم الفني',
      dataIndex: 'technical_number',
      key: 'technical_number',
    },
    {
      title: 'اسم المريض',
      dataIndex: 'patient_name',
      key: 'patient_name',
    },
    {
      title: 'الجهة المرسلة',
      dataIndex: 'sending_authority',
      key: 'sending_authority',
    },
    {
      title: 'تاريخ الفحص',
      dataIndex: 'exam_date',
      key: 'exam_date',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="primary" size="small" icon={<EditOutlined />} />
          <Button 
            type="primary" 
            danger 
            size="small" 
            icon={<DeleteOutlined />}
            onClick={() => deleteMutation.mutate(record.id)}
          />
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Card className="neumorphic-card" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            <ExperimentOutlined style={{ marginLeft: 8 }} />
            فحص الشدة
          </Title>
          
          <Space>
            <Search
              placeholder="البحث في الحالات..."
              allowClear
              style={{ width: 300 }}
              onSearch={setSearchText}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsModalVisible(true)}
            >
              إضافة حالة جديدة
            </Button>
          </Space>
        </div>
      </Card>

      <Card className="neumorphic-card">
        <Table
          columns={columns}
          dataSource={cases?.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} من ${total} حالة`,
          }}
        />
      </Card>

      <Modal
        title="إضافة حالة فحص شدة جديدة"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ padding: 20, textAlign: 'center' }}>
          <p>نموذج فحص الشدة سيتم إضافته قريباً</p>
        </div>
      </Modal>
    </div>
  )
}

export default IntensityExam
