#!/usr/bin/env python3
"""
اختبار أساسي جداً لمعرفة ما يعمل وما لا يعمل
"""
import sys
import os

print("🔍 اختبار أساسي لبرنامج شعبة الأحياء")
print("=" * 50)

# 1. فحص Python
print(f"🐍 Python: {sys.version}")
print(f"📁 المجلد الحالي: {os.getcwd()}")
print(f"📂 محتويات المجلد:")
for item in os.listdir('.'):
    if os.path.isfile(item):
        print(f"   📄 {item}")
    else:
        print(f"   📁 {item}/")

# 2. فحص استيراد المكتبات الأساسية
print("\n📚 فحص المكتبات:")

libraries = [
    ('http.server', 'مكتبة HTTP الأساسية'),
    ('json', 'مكتبة JSON'),
    ('datetime', 'مكتبة التاريخ'),
    ('sqlite3', 'قاعدة بيانات SQLite'),
]

working_libs = []
for lib, desc in libraries:
    try:
        __import__(lib)
        print(f"✅ {desc}")
        working_libs.append(lib)
    except ImportError:
        print(f"❌ {desc}")

# 3. فحص المكتبات الخارجية
print("\n📦 فحص المكتبات الخارجية:")
external_libs = [
    ('fastapi', 'FastAPI'),
    ('uvicorn', 'Uvicorn'),
    ('sqlalchemy', 'SQLAlchemy'),
    ('pydantic', 'Pydantic'),
]

working_external = []
for lib, desc in external_libs:
    try:
        __import__(lib)
        print(f"✅ {desc}")
        working_external.append(lib)
    except ImportError:
        print(f"❌ {desc} - غير مثبت")

# 4. إنشاء خادم بسيط بالمكتبات المتوفرة
print("\n🚀 إنشاء خادم بسيط...")

if 'fastapi' in working_external and 'uvicorn' in working_external:
    print("✅ سأنشئ خادم FastAPI")
    server_type = "fastapi"
elif 'http.server' in working_libs:
    print("✅ سأنشئ خادم HTTP بسيط")
    server_type = "basic"
else:
    print("❌ لا يمكن إنشاء أي خادم")
    server_type = None

# 5. كتابة ملف الخادم المناسب
if server_type == "fastapi":
    server_code = '''
from fastapi import FastAPI
import uvicorn

app = FastAPI(title="شعبة الأحياء - اختبار")

@app.get("/")
def home():
    return {
        "message": "مرحباً بكم في برنامج شعبة الأحياء",
        "status": "يعمل بنجاح",
        "type": "FastAPI Server"
    }

@app.get("/test")
def test():
    return {"test": "success", "message": "الاختبار نجح"}

if __name__ == "__main__":
    print("🚀 بدء تشغيل خادم FastAPI...")
    print("🌐 http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    with open('working_server.py', 'w', encoding='utf-8') as f:
        f.write(server_code)
    print("✅ تم إنشاء working_server.py (FastAPI)")

elif server_type == "basic":
    server_code = '''
from http.server import HTTPServer, BaseHTTPRequestHandler
import json

class Handler(BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.end_headers()
        
        if self.path == '/':
            response = {
                "message": "مرحباً بكم في برنامج شعبة الأحياء",
                "status": "يعمل بنجاح", 
                "type": "Basic HTTP Server"
            }
        elif self.path == '/test':
            response = {"test": "success", "message": "الاختبار نجح"}
        else:
            response = {"error": "الصفحة غير موجودة"}
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def log_message(self, format, *args):
        pass  # تعطيل رسائل السجل

if __name__ == "__main__":
    server = HTTPServer(('localhost', 8000), Handler)
    print("🚀 بدء تشغيل خادم HTTP بسيط...")
    print("🌐 http://localhost:8000")
    print("🛑 اضغط Ctrl+C لإيقاف الخادم")
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\\n🛑 تم إيقاف الخادم")
        server.shutdown()
'''
    
    with open('working_server.py', 'w', encoding='utf-8') as f:
        f.write(server_code)
    print("✅ تم إنشاء working_server.py (HTTP بسيط)")

# 6. النتائج والتوصيات
print("\n" + "=" * 50)
print("📋 النتائج:")

if server_type:
    print("✅ تم إنشاء خادم يعمل")
    print("🚀 لتشغيل الخادم:")
    print("   python working_server.py")
    print("🌐 ثم افتح: http://localhost:8000")
else:
    print("❌ لا يمكن إنشاء خادم")

if len(working_external) == 0:
    print("\n💡 لتثبيت المكتبات المطلوبة:")
    print("   pip install fastapi uvicorn")

print("\n📞 للمساعدة:")
print("   - شارك نتائج هذا الاختبار")
print("   - أخبرني أي رسائل خطأ تظهر")

input("\nاضغط Enter للخروج...")
