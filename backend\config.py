"""
إعدادات التطبيق
"""
import os
from pathlib import Path

# المجلد الأساسي
BASE_DIR = Path(__file__).parent

# إعدادات قاعدة البيانات
DATABASE_URL = f"sqlite:///{BASE_DIR}/data/forensic_biology.db"

# إعدادات الأمان
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# إعدادات الخادم
HOST = "0.0.0.0"
PORT = 8000
DEBUG = os.getenv("DEBUG", "False").lower() == "true"

# إعدادات CORS
ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:5173",
]

# مجلدات البيانات
DATA_DIR = BASE_DIR / "data"
REPORTS_DIR = DATA_DIR / "reports"
TEMPLATES_DIR = DATA_DIR / "templates"
BACKUPS_DIR = DATA_DIR / "backups"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [DATA_DIR, REPORTS_DIR, TEMPLATES_DIR, BACKUPS_DIR]:
    directory.mkdir(exist_ok=True)

# إعدادات التطبيق
APP_NAME = "برنامج شعبة الأحياء - قسم الطبابة العدلية"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "نظام إدارة قواعد بيانات شعبة الأحياء"

# إعدادات التسجيل
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

print(f"📁 مجلد البيانات: {DATA_DIR}")
print(f"🗄️ قاعدة البيانات: {DATABASE_URL}")
print(f"🔧 وضع التطوير: {DEBUG}")
