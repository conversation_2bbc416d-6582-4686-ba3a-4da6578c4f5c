"""
الخادم الرئيسي لبرنامج شعبة الأحياء - قسم الطبابة العدلية
"""
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import uvicorn
from datetime import datetime, timedelta
from typing import Optional
import os

# استيراد الوحدات المحلية
from database import get_db, init_db
from models import User, ActivityLog

# استيراد المسارات (سيتم إنشاؤها تدريجياً)
try:
    from routes import auth, age_estimation, intensity_exam, gender_determination, sexual_assault, reports, users, settings
    ROUTES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  تحذير: بعض المسارات غير متوفرة: {e}")
    ROUTES_AVAILABLE = False

# إنشاء تطبيق FastAPI
app = FastAPI(
    title="برنامج شعبة الأحياء - قسم الطبابة العدلية",
    description="نظام إدارة قواعد بيانات شعبة الأحياء",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# إعداد CORS للسماح بالوصول من الواجهة الأمامية
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد المصادقة
security = HTTPBearer()

# تسجيل المسارات (إذا كانت متوفرة)
if ROUTES_AVAILABLE:
    try:
        app.include_router(auth.router, prefix="/api/auth", tags=["المصادقة"])
        app.include_router(age_estimation.router, prefix="/api/age-estimation", tags=["تقدير الأعمار"])
        app.include_router(intensity_exam.router, prefix="/api/intensity-exam", tags=["فحص الشدة"])
        app.include_router(gender_determination.router, prefix="/api/gender-determination", tags=["تحديد الجنس"])
        app.include_router(sexual_assault.router, prefix="/api/sexual-assault", tags=["الوقوعات الجنسية"])
        app.include_router(reports.router, prefix="/api/reports", tags=["التقارير"])
        app.include_router(users.router, prefix="/api/users", tags=["إدارة المستخدمين"])
        app.include_router(settings.router, prefix="/api/settings", tags=["الإعدادات"])
        print("✅ تم تحميل جميع المسارات بنجاح")
    except Exception as e:
        print(f"⚠️  خطأ في تحميل بعض المسارات: {e}")
else:
    print("⚠️  تم تشغيل الخادم بدون بعض المسارات")

@app.on_event("startup")
async def startup_event():
    """أحداث بدء التشغيل"""
    print("🚀 بدء تشغيل خادم شعبة الأحياء...")
    
    # إنشاء قاعدة البيانات
    init_db()
    
    # إنشاء مجلدات ضرورية
    os.makedirs("data/reports", exist_ok=True)
    os.makedirs("data/templates", exist_ok=True)
    os.makedirs("data/backups", exist_ok=True)
    
    print("✅ تم تشغيل الخادم بنجاح")

@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "مرحباً بكم في برنامج شعبة الأحياء - قسم الطبابة العدلية",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/health")
async def health_check():
    """فحص صحة الخادم"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "database": "connected"
    }

@app.middleware("http")
async def log_requests(request, call_next):
    """تسجيل الطلبات"""
    start_time = datetime.now()
    
    # معالجة الطلب
    response = await call_next(request)
    
    # حساب وقت المعالجة
    process_time = (datetime.now() - start_time).total_seconds()
    
    # تسجيل معلومات الطلب
    print(f"📝 {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s")
    
    return response

def log_activity(db: Session, user_id: int, action: str, table_name: str = None, 
                record_id: int = None, details: str = None, ip_address: str = None):
    """تسجيل نشاط المستخدم"""
    try:
        activity = ActivityLog(
            user_id=user_id,
            action=action,
            table_name=table_name,
            record_id=record_id,
            details=details,
            ip_address=ip_address
        )
        db.add(activity)
        db.commit()
    except Exception as e:
        print(f"خطأ في تسجيل النشاط: {e}")

if __name__ == "__main__":
    print("🔧 تشغيل الخادم في وضع التطوير...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
