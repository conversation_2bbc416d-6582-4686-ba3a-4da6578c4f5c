"""
نماذج قاعدة البيانات لبرنامج شعبة الأحياء
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base

class User(Base):
    """نموذج المستخدمين"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=False)
    role = Column(String(20), default="user")  # admin, manager, user
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_login = Column(DateTime(timezone=True))

class AgeEstimationCase(Base):
    """نموذج حالات تقدير الأعمار"""
    __tablename__ = "age_estimation_cases"
    
    id = Column(Integer, primary_key=True, index=True)
    technical_number = Column(String(50), unique=True, nullable=False)  # الرقم الفني
    sending_authority = Column(String(200), nullable=False)  # الجهة المرسلة
    court_letter_number = Column(String(100))  # رقم كتاب المحكمة
    court_letter_date = Column(DateTime)  # تاريخ كتاب المحكمة
    family_head_name = Column(String(100), nullable=False)  # اسم رب الأسرة
    committee_meeting_date = Column(DateTime, nullable=False)  # تاريخ اجتماع اللجنة
    notes = Column(Text)  # ملاحظات عامة
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    children = relationship("Child", back_populates="case", cascade="all, delete-orphan")
    creator = relationship("User")

class Child(Base):
    """نموذج الأطفال في حالات تقدير الأعمار"""
    __tablename__ = "children"
    
    id = Column(Integer, primary_key=True, index=True)
    case_id = Column(Integer, ForeignKey("age_estimation_cases.id"), nullable=False)
    sequence = Column(Integer, nullable=False)  # التسلسل
    name = Column(String(100), nullable=False)  # الاسم
    gender = Column(String(10), nullable=False)  # الجنس (ذكر/أنثى)
    age_numeric = Column(Float)  # العمر رقماً
    age_text = Column(String(200))  # العمر كتابة
    notes = Column(Text)  # الملاحظات
    
    # العلاقات
    case = relationship("AgeEstimationCase", back_populates="children")

class IntensityExamCase(Base):
    """نموذج حالات فحص الشدة"""
    __tablename__ = "intensity_exam_cases"
    
    id = Column(Integer, primary_key=True, index=True)
    technical_number = Column(String(50), unique=True, nullable=False)
    sending_authority = Column(String(200), nullable=False)
    patient_name = Column(String(100), nullable=False)
    patient_age = Column(Integer)
    patient_gender = Column(String(10))
    exam_date = Column(DateTime, nullable=False)
    exam_results = Column(Text)
    medical_notes = Column(Text)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    creator = relationship("User")

class GenderDeterminationCase(Base):
    """نموذج حالات تحديد الجنس"""
    __tablename__ = "gender_determination_cases"
    
    id = Column(Integer, primary_key=True, index=True)
    technical_number = Column(String(50), unique=True, nullable=False)
    sending_authority = Column(String(200), nullable=False)
    subject_name = Column(String(100), nullable=False)
    subject_age = Column(Integer)
    exam_date = Column(DateTime, nullable=False)
    determination_result = Column(String(20))  # ذكر/أنثى/غير محدد
    exam_method = Column(String(100))  # طريقة الفحص
    medical_notes = Column(Text)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    creator = relationship("User")

class SexualAssaultCase(Base):
    """نموذج حالات الوقوعات الجنسية"""
    __tablename__ = "sexual_assault_cases"
    
    id = Column(Integer, primary_key=True, index=True)
    technical_number = Column(String(50), unique=True, nullable=False)
    sending_authority = Column(String(200), nullable=False)
    victim_name = Column(String(100), nullable=False)
    victim_age = Column(Integer)
    victim_gender = Column(String(10))
    incident_date = Column(DateTime)
    exam_date = Column(DateTime, nullable=False)
    exam_results = Column(Text)
    medical_findings = Column(Text)
    psychological_notes = Column(Text)
    is_confidential = Column(Boolean, default=True)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    creator = relationship("User")

class ActivityLog(Base):
    """نموذج سجل العمليات"""
    __tablename__ = "activity_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    action = Column(String(100), nullable=False)  # نوع العملية
    table_name = Column(String(50))  # اسم الجدول المتأثر
    record_id = Column(Integer)  # معرف السجل المتأثر
    details = Column(Text)  # تفاصيل العملية
    ip_address = Column(String(45))
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    
    user = relationship("User")

class SystemSettings(Base):
    """نموذج إعدادات النظام"""
    __tablename__ = "system_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, nullable=False)
    value = Column(Text)
    description = Column(String(255))
    updated_by = Column(Integer, ForeignKey("users.id"))
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    updater = relationship("User")
