@echo off
chcp 65001 >nul
title برنامج شعبة الأحياء

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                برنامج شعبة الأحياء                          ║
echo ║                قسم الطبابة العدلية                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 فحص Python...

REM جرب py أولاً
py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python موجود
    echo.
    echo 🚀 بدء تشغيل البرنامج...
    py run.py
    goto end
)

REM جرب python
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python موجود
    echo.
    echo 🚀 بدء تشغيل البرنامج...
    python run.py
    goto end
)

REM جرب python3
python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python موجود
    echo.
    echo 🚀 بدء تشغيل البرنامج...
    python3 run.py
    goto end
)

echo ❌ Python غير موجود
echo.
echo 💡 يرجى تثبيت Python من:
echo    https://python.org/downloads/
echo.
echo ⚠️  تأكد من اختيار "Add Python to PATH"
echo.

:end
echo.
pause
