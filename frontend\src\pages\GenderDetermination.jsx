import React, { useState } from 'react'
import { Card, Table, Button, Space, Input, Modal, Typography, message } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, TeamOutlined } from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import dayjs from 'dayjs'
import { genderDeterminationAPI } from '../services/api'

const { Title } = Typography
const { Search } = Input

const GenderDetermination = () => {
  const [searchText, setSearchText] = useState('')
  const [isModalVisible, setIsModalVisible] = useState(false)
  const queryClient = useQueryClient()

  const { data: cases, isLoading } = useQuery(
    ['gender-determination-cases', searchText],
    () => genderDeterminationAPI.getAll({ search: searchText })
  )

  const deleteMutation = useMutation(genderDeterminationAPI.delete, {
    onSuccess: () => {
      message.success('تم حذف الحالة بنجاح')
      queryClient.invalidateQueries('gender-determination-cases')
    },
  })

  const columns = [
    {
      title: 'الرقم الفني',
      dataIndex: 'technical_number',
      key: 'technical_number',
    },
    {
      title: 'اسم الشخص',
      dataIndex: 'subject_name',
      key: 'subject_name',
    },
    {
      title: 'الجهة المرسلة',
      dataIndex: 'sending_authority',
      key: 'sending_authority',
    },
    {
      title: 'نتيجة التحديد',
      dataIndex: 'determination_result',
      key: 'determination_result',
    },
    {
      title: 'تاريخ الفحص',
      dataIndex: 'exam_date',
      key: 'exam_date',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="primary" size="small" icon={<EditOutlined />} />
          <Button 
            type="primary" 
            danger 
            size="small" 
            icon={<DeleteOutlined />}
            onClick={() => deleteMutation.mutate(record.id)}
          />
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Card className="neumorphic-card" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            <TeamOutlined style={{ marginLeft: 8 }} />
            تحديد الجنس
          </Title>
          
          <Space>
            <Search
              placeholder="البحث في الحالات..."
              allowClear
              style={{ width: 300 }}
              onSearch={setSearchText}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsModalVisible(true)}
            >
              إضافة حالة جديدة
            </Button>
          </Space>
        </div>
      </Card>

      <Card className="neumorphic-card">
        <Table
          columns={columns}
          dataSource={cases?.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} من ${total} حالة`,
          }}
        />
      </Card>

      <Modal
        title="إضافة حالة تحديد جنس جديدة"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ padding: 20, textAlign: 'center' }}>
          <p>نموذج تحديد الجنس سيتم إضافته قريباً</p>
        </div>
      </Modal>
    </div>
  )
}

export default GenderDetermination
