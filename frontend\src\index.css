/* إعدادات عامة للعربية */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  text-align: right;
  background-color: #f0f2f5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* تخصيص Ant Design للعربية */
.ant-layout {
  direction: rtl !important;
}

.ant-menu {
  direction: rtl !important;
}

.ant-table {
  direction: rtl !important;
}

.ant-form {
  direction: rtl !important;
}

.ant-input {
  direction: rtl !important;
  text-align: right !important;
}

.ant-select {
  direction: rtl !important;
}

.ant-pagination {
  direction: rtl !important;
}

/* تصميم نيومورفيك */
.neumorphic-card {
  background: #e0e5ec;
  border-radius: 20px;
  box-shadow: 
    9px 9px 16px #a3b1c6,
    -9px -9px 16px #ffffff;
  border: none;
  padding: 24px;
  margin: 16px 0;
}

.neumorphic-button {
  background: #e0e5ec !important;
  border: none !important;
  border-radius: 15px !important;
  box-shadow: 
    5px 5px 10px #a3b1c6,
    -5px -5px 10px #ffffff !important;
  transition: all 0.2s ease !important;
  color: #333 !important;
}

.neumorphic-button:hover {
  box-shadow: 
    3px 3px 6px #a3b1c6,
    -3px -3px 6px #ffffff !important;
  color: #1890ff !important;
}

.neumorphic-button:active,
.neumorphic-button:focus {
  box-shadow: 
    inset 3px 3px 6px #a3b1c6,
    inset -3px -3px 6px #ffffff !important;
}

.neumorphic-input {
  background: #e0e5ec !important;
  border: none !important;
  border-radius: 15px !important;
  box-shadow: 
    inset 5px 5px 10px #a3b1c6,
    inset -5px -5px 10px #ffffff !important;
  padding: 12px 16px !important;
}

.neumorphic-input:focus {
  box-shadow: 
    inset 3px 3px 6px #a3b1c6,
    inset -3px -3px 6px #ffffff !important;
  border: none !important;
  outline: none !important;
}

/* تخصيص الجدول */
.ant-table-thead > tr > th {
  background: #e0e5ec !important;
  border-bottom: 1px solid #d9d9d9 !important;
  text-align: center !important;
  font-weight: bold !important;
}

.ant-table-tbody > tr > td {
  text-align: center !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5 !important;
}

/* تخصيص القائمة الجانبية */
.ant-layout-sider {
  background: #001529 !important;
}

.ant-menu-dark {
  background: #001529 !important;
}

.ant-menu-dark .ant-menu-item {
  color: rgba(255, 255, 255, 0.65) !important;
}

.ant-menu-dark .ant-menu-item:hover {
  color: #fff !important;
  background-color: #1890ff !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
  color: #fff !important;
}

/* تخصيص الهيدر */
.ant-layout-header {
  background: #001529 !important;
  padding: 0 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

/* تخصيص المحتوى */
.ant-layout-content {
  margin: 24px 16px !important;
  padding: 24px !important;
  background: #fff !important;
  border-radius: 20px !important;
  box-shadow: 
    9px 9px 16px #a3b1c6,
    -9px -9px 16px #ffffff !important;
  min-height: 280px !important;
}

/* تخصيص النماذج */
.ant-form-item-label > label {
  font-weight: bold !important;
  color: #333 !important;
}

/* تخصيص الأزرار */
.ant-btn-primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
  border-radius: 15px !important;
  box-shadow: 
    5px 5px 10px rgba(24, 144, 255, 0.3),
    -5px -5px 10px #ffffff !important;
}

.ant-btn-primary:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
  box-shadow: 
    3px 3px 6px rgba(24, 144, 255, 0.3),
    -3px -3px 6px #ffffff !important;
}

/* تخصيص الكروت */
.ant-card {
  border-radius: 20px !important;
  box-shadow: 
    9px 9px 16px #a3b1c6,
    -9px -9px 16px #ffffff !important;
  border: none !important;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0 !important;
  border-radius: 20px 20px 0 0 !important;
}

/* تخصيص الإحصائيات */
.stats-card {
  text-align: center !important;
  padding: 24px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border-radius: 20px !important;
  box-shadow: 
    9px 9px 16px rgba(102, 126, 234, 0.3),
    -9px -9px 16px #ffffff !important;
}

.stats-number {
  font-size: 2.5rem !important;
  font-weight: bold !important;
  margin-bottom: 8px !important;
}

.stats-label {
  font-size: 1rem !important;
  opacity: 0.9 !important;
}

/* تخصيص الرسوم البيانية */
.chart-container {
  background: #fff !important;
  border-radius: 20px !important;
  padding: 24px !important;
  box-shadow: 
    9px 9px 16px #a3b1c6,
    -9px -9px 16px #ffffff !important;
  margin: 16px 0 !important;
}

/* تخصيص التنبيهات */
.ant-notification {
  direction: rtl !important;
}

.ant-message {
  direction: rtl !important;
}

/* تخصيص التحميل */
.loading-container {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  height: 200px !important;
}

/* تخصيص الأخطاء */
.error-container {
  text-align: center !important;
  padding: 48px !important;
  color: #ff4d4f !important;
}

/* تخصيص الفارغ */
.empty-container {
  text-align: center !important;
  padding: 48px !important;
  color: #999 !important;
}
