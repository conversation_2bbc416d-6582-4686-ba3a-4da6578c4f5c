#!/usr/bin/env python3
"""
اختبار شامل لنظام شعبة الأحياء
"""
import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def test_python():
    """اختبار Python"""
    print("🐍 اختبار Python...")
    try:
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
            return True
        else:
            print(f"❌ Python {version.major}.{version.minor} - يتطلب 3.8+")
            return False
    except Exception as e:
        print(f"❌ خطأ في Python: {e}")
        return False

def test_node():
    """اختبار Node.js"""
    print("📦 اختبار Node.js...")
    try:
        result = subprocess.run(['node', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js {version}")
            return True
        else:
            print("❌ Node.js غير متوفر")
            return False
    except Exception as e:
        print(f"❌ خطأ في Node.js: {e}")
        return False

def test_backend_imports():
    """اختبار استيراد وحدات الخادم"""
    print("📚 اختبار استيراد وحدات الخادم...")
    
    backend_path = Path(__file__).parent / 'backend'
    sys.path.insert(0, str(backend_path))
    
    try:
        # اختبار الاستيرادات الأساسية
        from database import init_db, get_db
        print("✅ database")
        
        from models import User, AgeEstimationCase
        print("✅ models")
        
        from fastapi import FastAPI
        print("✅ FastAPI")
        
        import uvicorn
        print("✅ uvicorn")
        
        return True
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("🗄️ اختبار قاعدة البيانات...")
    
    try:
        backend_path = Path(__file__).parent / 'backend'
        os.chdir(backend_path)
        
        from database import init_db, SessionLocal
        from models import User
        
        # إنشاء قاعدة البيانات
        init_db()
        print("✅ تم إنشاء قاعدة البيانات")
        
        # اختبار الاتصال
        db = SessionLocal()
        users_count = db.query(User).count()
        db.close()
        print(f"✅ الاتصال بقاعدة البيانات - عدد المستخدمين: {users_count}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def start_backend_server():
    """بدء تشغيل الخادم الخلفي"""
    print("🚀 بدء تشغيل الخادم الخلفي...")
    
    try:
        backend_path = Path(__file__).parent / 'backend'
        
        # تشغيل الخادم في عملية منفصلة
        process = subprocess.Popen([
            sys.executable, 'run_server.py'
        ], cwd=backend_path, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # انتظار بدء الخادم
        print("⏳ انتظار بدء الخادم...")
        time.sleep(5)
        
        # اختبار الاتصال
        try:
            response = requests.get('http://localhost:8000/', timeout=5)
            if response.status_code == 200:
                print("✅ الخادم يعمل بنجاح")
                return process
            else:
                print(f"❌ الخادم يرد بكود: {response.status_code}")
                process.terminate()
                return None
        except requests.exceptions.RequestException as e:
            print(f"❌ لا يمكن الوصول للخادم: {e}")
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        return None

def test_api_endpoints():
    """اختبار نقاط API"""
    print("🔗 اختبار نقاط API...")
    
    try:
        # اختبار الصفحة الرئيسية
        response = requests.get('http://localhost:8000/', timeout=5)
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية")
        else:
            print(f"❌ الصفحة الرئيسية: {response.status_code}")
        
        # اختبار فحص الصحة
        response = requests.get('http://localhost:8000/api/health', timeout=5)
        if response.status_code == 200:
            print("✅ فحص الصحة")
        else:
            print(f"❌ فحص الصحة: {response.status_code}")
        
        # اختبار وثائق API
        response = requests.get('http://localhost:8000/api/docs', timeout=5)
        if response.status_code == 200:
            print("✅ وثائق API")
        else:
            print(f"❌ وثائق API: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لنظام شعبة الأحياء")
    print("=" * 60)
    
    # اختبار المتطلبات الأساسية
    if not test_python():
        print("❌ فشل اختبار Python")
        return
    
    if not test_node():
        print("⚠️ Node.js غير متوفر - لن تعمل الواجهة الأمامية")
    
    # اختبار الخادم الخلفي
    if not test_backend_imports():
        print("❌ فشل اختبار استيراد وحدات الخادم")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r backend/requirements.txt")
        return
    
    if not test_database():
        print("❌ فشل اختبار قاعدة البيانات")
        return
    
    # تشغيل الخادم واختباره
    server_process = start_backend_server()
    if server_process:
        try:
            if test_api_endpoints():
                print("\n" + "=" * 60)
                print("🎉 جميع الاختبارات نجحت!")
                print("✅ النظام جاهز للاستخدام")
                print("\n📋 معلومات الوصول:")
                print("   - API الخلفي: http://localhost:8000")
                print("   - وثائق API: http://localhost:8000/api/docs")
                print("\n👤 بيانات تسجيل الدخول:")
                print("   - المدير: admin / admin123")
                print("   - المستخدم: user / user123")
                print("\n🛑 اضغط Enter لإيقاف الخادم...")
                input()
            else:
                print("❌ فشل اختبار API")
        finally:
            print("🛑 إيقاف الخادم...")
            server_process.terminate()
            server_process.wait()
    else:
        print("❌ فشل في تشغيل الخادم")

if __name__ == "__main__":
    main()
