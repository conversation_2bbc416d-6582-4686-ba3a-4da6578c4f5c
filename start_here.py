#!/usr/bin/env python3
"""
نقطة البداية لبرنامج شعبة الأحياء
"""
import os
import sys
import subprocess

def print_header():
    """طباعة رأس البرنامج"""
    print("╔" + "═" * 58 + "╗")
    print("║" + " " * 58 + "║")
    print("║" + "برنامج شعبة الأحياء - قسم الطبابة العدلية".center(58) + "║")
    print("║" + "الإصدار 1.0.0".center(58) + "║")
    print("║" + " " * 58 + "║")
    print("╚" + "═" * 58 + "╝")
    print()

def check_python():
    """فحص Python"""
    version = sys.version_info
    print(f"🐍 Python {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        print("💡 يرجى تحميل Python من: https://python.org")
        return False
    
    print("✅ إصدار Python مناسب")
    return True

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n📋 اختر طريقة التشغيل:")
    print("   1️⃣  تشخيص النظام (مستحسن للمرة الأولى)")
    print("   2️⃣  تثبيت المتطلبات فقط")
    print("   3️⃣  تشغيل بسيط (بدون قاعدة بيانات)")
    print("   4️⃣  تشغيل سريع (مع قاعدة البيانات)")
    print("   5️⃣  تشغيل كامل (جميع الميزات)")
    print("   6️⃣  عرض دليل استكشاف الأخطاء")
    print("   0️⃣  خروج")
    print()

def run_diagnosis():
    """تشغيل التشخيص"""
    print("🔍 تشغيل تشخيص النظام...")
    if os.path.exists('diagnose.py'):
        subprocess.run([sys.executable, 'diagnose.py'])
    else:
        print("❌ ملف التشخيص غير موجود")

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    if os.path.exists('install_requirements.py'):
        subprocess.run([sys.executable, 'install_requirements.py'])
    else:
        print("❌ ملف تثبيت المتطلبات غير موجود")

def run_simple():
    """تشغيل بسيط"""
    print("🚀 تشغيل الخادم البسيط...")
    if os.path.exists('simple_start.py'):
        subprocess.run([sys.executable, 'simple_start.py'])
    else:
        print("❌ ملف التشغيل البسيط غير موجود")

def run_quick():
    """تشغيل سريع"""
    print("🚀 تشغيل سريع...")
    if os.path.exists('quick_start.py'):
        subprocess.run([sys.executable, 'quick_start.py'])
    else:
        print("❌ ملف التشغيل السريع غير موجود")

def run_full():
    """تشغيل كامل"""
    print("🚀 تشغيل النظام الكامل...")
    if os.path.exists('run_full_system.py'):
        subprocess.run([sys.executable, 'run_full_system.py'])
    else:
        print("❌ ملف التشغيل الكامل غير موجود")

def show_troubleshooting():
    """عرض دليل استكشاف الأخطاء"""
    if os.path.exists('استكشاف_الأخطاء.md'):
        print("📖 فتح دليل استكشاف الأخطاء...")
        try:
            # محاولة فتح الملف بالبرنامج الافتراضي
            if os.name == 'nt':  # Windows
                os.startfile('استكشاف_الأخطاء.md')
            else:  # Linux/Mac
                subprocess.run(['xdg-open', 'استكشاف_الأخطاء.md'])
        except:
            print("💡 يرجى فتح ملف 'استكشاف_الأخطاء.md' يدوياً")
    else:
        print("❌ دليل استكشاف الأخطاء غير موجود")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص Python
    if not check_python():
        input("اضغط Enter للخروج...")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("اختر رقماً (1-6 أو 0 للخروج): ").strip()
            
            if choice == '0':
                print("👋 شكراً لاستخدام برنامج شعبة الأحياء")
                break
            elif choice == '1':
                run_diagnosis()
            elif choice == '2':
                install_requirements()
            elif choice == '3':
                run_simple()
            elif choice == '4':
                run_quick()
            elif choice == '5':
                run_full()
            elif choice == '6':
                show_troubleshooting()
            else:
                print("❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى")
            
            if choice in ['1', '2', '3', '4', '5']:
                input("\nاضغط Enter للعودة للقائمة الرئيسية...")
                
        except KeyboardInterrupt:
            print("\n👋 تم إنهاء البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
            input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
