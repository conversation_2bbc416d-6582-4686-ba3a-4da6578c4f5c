"""
مسارات تقدير الأعمار
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from database import get_db
from models import AgeEstimationCase, Child, User
from routes.auth import get_current_user

router = APIRouter()

# نماذج البيانات
class ChildCreate(BaseModel):
    sequence: int
    name: str
    gender: str
    age_numeric: Optional[float] = None
    age_text: Optional[str] = None
    notes: Optional[str] = None

class ChildResponse(BaseModel):
    id: int
    sequence: int
    name: str
    gender: str
    age_numeric: Optional[float]
    age_text: Optional[str]
    notes: Optional[str]

    class Config:
        from_attributes = True

class AgeEstimationCaseCreate(BaseModel):
    technical_number: str
    sending_authority: str
    court_letter_number: Optional[str] = None
    court_letter_date: Optional[datetime] = None
    family_head_name: str
    committee_meeting_date: datetime
    notes: Optional[str] = None
    children: List[ChildCreate] = []

class AgeEstimationCaseUpdate(BaseModel):
    technical_number: Optional[str] = None
    sending_authority: Optional[str] = None
    court_letter_number: Optional[str] = None
    court_letter_date: Optional[datetime] = None
    family_head_name: Optional[str] = None
    committee_meeting_date: Optional[datetime] = None
    notes: Optional[str] = None

class AgeEstimationCaseResponse(BaseModel):
    id: int
    technical_number: str
    sending_authority: str
    court_letter_number: Optional[str]
    court_letter_date: Optional[datetime]
    family_head_name: str
    committee_meeting_date: datetime
    notes: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    children: List[ChildResponse] = []

    class Config:
        from_attributes = True

# المسارات
@router.get("/", response_model=List[AgeEstimationCaseResponse])
async def get_age_estimation_cases(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة حالات تقدير الأعمار"""
    query = db.query(AgeEstimationCase)
    
    if search:
        query = query.filter(
            (AgeEstimationCase.technical_number.contains(search)) |
            (AgeEstimationCase.family_head_name.contains(search)) |
            (AgeEstimationCase.sending_authority.contains(search))
        )
    
    cases = query.offset(skip).limit(limit).all()
    return cases

@router.get("/{case_id}", response_model=AgeEstimationCaseResponse)
async def get_age_estimation_case(
    case_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على حالة تقدير أعمار محددة"""
    case = db.query(AgeEstimationCase).filter(AgeEstimationCase.id == case_id).first()
    if not case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحالة غير موجودة"
        )
    return case

@router.post("/", response_model=AgeEstimationCaseResponse)
async def create_age_estimation_case(
    case_data: AgeEstimationCaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء حالة تقدير أعمار جديدة"""
    # التحقق من عدم تكرار الرقم الفني
    existing_case = db.query(AgeEstimationCase).filter(
        AgeEstimationCase.technical_number == case_data.technical_number
    ).first()
    
    if existing_case:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="الرقم الفني موجود بالفعل"
        )
    
    # إنشاء الحالة الجديدة
    new_case = AgeEstimationCase(
        technical_number=case_data.technical_number,
        sending_authority=case_data.sending_authority,
        court_letter_number=case_data.court_letter_number,
        court_letter_date=case_data.court_letter_date,
        family_head_name=case_data.family_head_name,
        committee_meeting_date=case_data.committee_meeting_date,
        notes=case_data.notes,
        created_by=current_user.id
    )
    
    db.add(new_case)
    db.flush()  # للحصول على معرف الحالة
    
    # إضافة الأطفال
    for child_data in case_data.children:
        child = Child(
            case_id=new_case.id,
            sequence=child_data.sequence,
            name=child_data.name,
            gender=child_data.gender,
            age_numeric=child_data.age_numeric,
            age_text=child_data.age_text,
            notes=child_data.notes
        )
        db.add(child)
    
    db.commit()
    db.refresh(new_case)
    
    return new_case

@router.put("/{case_id}", response_model=AgeEstimationCaseResponse)
async def update_age_estimation_case(
    case_id: int,
    case_data: AgeEstimationCaseUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تحديث حالة تقدير أعمار"""
    case = db.query(AgeEstimationCase).filter(AgeEstimationCase.id == case_id).first()
    if not case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحالة غير موجودة"
        )
    
    # تحديث البيانات
    for field, value in case_data.dict(exclude_unset=True).items():
        setattr(case, field, value)
    
    db.commit()
    db.refresh(case)
    
    return case

@router.delete("/{case_id}")
async def delete_age_estimation_case(
    case_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """حذف حالة تقدير أعمار"""
    case = db.query(AgeEstimationCase).filter(AgeEstimationCase.id == case_id).first()
    if not case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحالة غير موجودة"
        )
    
    db.delete(case)
    db.commit()
    
    return {"message": "تم حذف الحالة بنجاح"}

# مسارات إدارة الأطفال
@router.post("/{case_id}/children", response_model=ChildResponse)
async def add_child_to_case(
    case_id: int,
    child_data: ChildCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إضافة طفل إلى حالة"""
    case = db.query(AgeEstimationCase).filter(AgeEstimationCase.id == case_id).first()
    if not case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحالة غير موجودة"
        )
    
    child = Child(
        case_id=case_id,
        sequence=child_data.sequence,
        name=child_data.name,
        gender=child_data.gender,
        age_numeric=child_data.age_numeric,
        age_text=child_data.age_text,
        notes=child_data.notes
    )
    
    db.add(child)
    db.commit()
    db.refresh(child)
    
    return child

@router.delete("/children/{child_id}")
async def delete_child(
    child_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """حذف طفل من حالة"""
    child = db.query(Child).filter(Child.id == child_id).first()
    if not child:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الطفل غير موجود"
        )
    
    db.delete(child)
    db.commit()
    
    return {"message": "تم حذف الطفل بنجاح"}
