import React, { useState } from 'react'
import { <PERSON>, Row, Col, But<PERSON>, DatePicker, Typography, Space, message, Statistic } from 'antd'
import { DownloadOutlined, BarChartOutlined, FileTextOutlined, CalendarOutlined } from '@ant-design/icons'
import { useQuery } from 'react-query'
import dayjs from 'dayjs'
import { reportsAPI } from '../services/api'

const { Title, Text } = Typography

const Reports = () => {
  const [selectedDate, setSelectedDate] = useState(dayjs())
  const [loading, setLoading] = useState(false)

  const { data: stats } = useQuery(
    ['reports-stats'],
    () => reportsAPI.getStats()
  )

  const downloadUnifiedReport = async () => {
    try {
      setLoading(true)
      const response = await reportsAPI.generateUnifiedReport(selectedDate.format('YYYY-MM-DD'))
      
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `تقرير_موحد_${selectedDate.format('YYYY-MM-DD')}.docx`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      message.success('تم تحميل التقرير الموحد بنجاح')
    } catch (error) {
      message.error('خطأ في تحميل التقرير الموحد')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <Card className="neumorphic-card" style={{ marginBottom: 16 }}>
        <Title level={3} style={{ margin: 0 }}>
          <BarChartOutlined style={{ marginLeft: 8 }} />
          التقارير والإحصائيات
        </Title>
      </Card>

      {/* الإحصائيات العامة */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="neumorphic-card stats-card">
            <Statistic
              title="إجمالي الحالات"
              value={stats?.data?.total_cases || 0}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="neumorphic-card stats-card">
            <Statistic
              title="الحالات الشهرية"
              value={stats?.data?.monthly_cases || 0}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="neumorphic-card stats-card">
            <Statistic
              title="الحالات السنوية"
              value={stats?.data?.yearly_cases || 0}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="neumorphic-card stats-card">
            <Statistic
              title="إجمالي الأطفال"
              value={Object.values(stats?.data?.gender_distribution || {}).reduce((a, b) => a + b, 0)}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* تحميل التقارير */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="التقرير الموحد" className="neumorphic-card">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text>اختر تاريخ اجتماع اللجنة لتحميل التقرير الموحد:</Text>
              
              <DatePicker
                value={selectedDate}
                onChange={setSelectedDate}
                style={{ width: '100%' }}
                placeholder="اختر التاريخ"
              />
              
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={downloadUnifiedReport}
                loading={loading}
                block
              >
                تحميل التقرير الموحد
              </Button>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="التقارير المفردة" className="neumorphic-card">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text>
                يمكنك تحميل التقارير المفردة من صفحة تقدير الأعمار
                عن طريق الضغط على زر التحميل بجانب كل حالة.
              </Text>
              
              <Button
                type="default"
                onClick={() => window.location.href = '/age-estimation'}
                block
              >
                الذهاب إلى تقدير الأعمار
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* توزيع البيانات */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24}>
          <Card title="توزيع الجنس" className="neumorphic-card">
            <Row gutter={[16, 16]}>
              {Object.entries(stats?.data?.gender_distribution || {}).map(([gender, count]) => (
                <Col xs={12} sm={8} md={6} key={gender}>
                  <Card className="neumorphic-card" style={{ textAlign: 'center' }}>
                    <Statistic
                      title={gender}
                      value={count}
                      valueStyle={{ 
                        color: gender === 'ذكر' ? '#1890ff' : '#ff69b4' 
                      }}
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* الفئات العمرية */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24}>
          <Card title="توزيع الفئات العمرية" className="neumorphic-card">
            <Row gutter={[16, 16]}>
              {Object.entries(stats?.data?.age_groups || {}).map(([ageGroup, count]) => (
                <Col xs={12} sm={8} md={6} lg={4} key={ageGroup}>
                  <Card className="neumorphic-card" style={{ textAlign: 'center' }}>
                    <Statistic
                      title={ageGroup}
                      value={count}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Reports
