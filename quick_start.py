#!/usr/bin/env python3
"""
تشغيل سريع لبرنامج شعبة الأحياء
"""
import os
import sys
import subprocess

def main():
    print("🏥 برنامج شعبة الأحياء - تشغيل سريع")
    print("=" * 40)
    
    # التحقق من Python
    if sys.version_info < (3, 8):
        print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor} - يتطلب 3.8+")
        input("اضغط Enter للخروج...")
        return
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # التحقق من وجود مجلد backend
    if not os.path.exists('backend'):
        print("❌ مجلد backend غير موجود")
        input("اضغط Enter للخروج...")
        return
    
    # الانتقال إلى مجلد backend
    os.chdir('backend')
    
    # التحقق من ملف requirements.txt
    if not os.path.exists('requirements.txt'):
        print("❌ ملف requirements.txt غير موجود")
        input("اضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        print("✅ تم تثبيت المتطلبات")
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        print("💡 جرب تشغيل: python ../diagnose.py")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء المستخدمين
    print("👤 إنشاء المستخدمين...")
    try:
        subprocess.run([sys.executable, 'create_admin.py'], check=True)
        print("✅ تم إنشاء المستخدمين")
    except subprocess.CalledProcessError:
        print("⚠️ تحذير: مشكلة في إنشاء المستخدمين")
    
    # تشغيل الخادم
    print("🚀 بدء تشغيل الخادم...")
    print("🌐 الخادم سيكون متاحاً على: http://localhost:8000")
    print("📚 وثائق API: http://localhost:8000/api/docs")
    print("🛑 اضغط Ctrl+C لإيقاف الخادم")
    print("-" * 40)
    
    try:
        # محاولة تشغيل الخادم الرئيسي
        if os.path.exists('run_server.py'):
            subprocess.run([sys.executable, 'run_server.py'])
        elif os.path.exists('simple_server.py'):
            subprocess.run([sys.executable, 'simple_server.py'])
        else:
            # إنشاء خادم بسيط
            create_minimal_server()
            subprocess.run([sys.executable, 'minimal_server.py'])
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        print("💡 جرب تشغيل: python ../diagnose.py")
    
    input("\nاضغط Enter للخروج...")

def create_minimal_server():
    """إنشاء خادم بسيط جداً"""
    minimal_code = '''
from fastapi import FastAPI
import uvicorn

app = FastAPI(title="شعبة الأحياء - خادم بسيط")

@app.get("/")
def home():
    return {
        "message": "مرحباً بكم في برنامج شعبة الأحياء",
        "status": "يعمل",
        "version": "1.0.0"
    }

@app.get("/health")
def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    with open('minimal_server.py', 'w', encoding='utf-8') as f:
        f.write(minimal_code)

if __name__ == "__main__":
    main()
