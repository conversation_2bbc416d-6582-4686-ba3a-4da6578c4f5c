import React from 'react'
import { Layout, Menu } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  DashboardOutlined,
  UserOutlined,
  ExperimentOutlined,
  TeamOutlined,
  HeartOutlined,
  FileTextOutlined,
  SettingOutlined,
  BarChartOutlined,
} from '@ant-design/icons'
import { useAuth } from '../../contexts/AuthContext'

const { Sider } = Layout

const Sidebar = ({ collapsed }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, isAdmin, isManager } = useAuth()

  // عناصر القائمة
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: 'لوحة التحكم',
    },
    {
      key: '/age-estimation',
      icon: <UserOutlined />,
      label: 'تقدير الأعمار',
    },
    {
      key: '/intensity-exam',
      icon: <ExperimentOutlined />,
      label: 'فحص الشدة',
    },
    {
      key: '/gender-determination',
      icon: <TeamOutlined />,
      label: 'تحديد الجنس',
    },
    // الوقوعات الجنسية - للمديرين فقط
    ...(isManager ? [{
      key: '/sexual-assault',
      icon: <HeartOutlined />,
      label: 'الوقوعات الجنسية',
    }] : []),
    {
      key: '/reports',
      icon: <BarChartOutlined />,
      label: 'التقارير والإحصائيات',
    },
    // إدارة المستخدمين - للمديرين فقط
    ...(isAdmin ? [{
      key: '/users',
      icon: <FileTextOutlined />,
      label: 'إدارة المستخدمين',
    }] : []),
    // الإعدادات - للمديرين فقط
    ...(isManager ? [{
      key: '/settings',
      icon: <SettingOutlined />,
      label: 'الإعدادات',
    }] : []),
  ]

  const handleMenuClick = ({ key }) => {
    navigate(key)
  }

  return (
    <Sider 
      trigger={null} 
      collapsible 
      collapsed={collapsed}
      className="ant-layout-sider"
      width={250}
      style={{
        overflow: 'auto',
        height: '100vh',
        position: 'fixed',
        right: 0,
        top: 0,
        bottom: 0,
      }}
    >
      {/* شعار التطبيق */}
      <div 
        style={{
          height: 64,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: collapsed ? '16px' : '18px',
          fontWeight: 'bold',
          borderBottom: '1px solid #001529',
          padding: '0 16px',
        }}
      >
        {collapsed ? 'ش.أ' : 'شعبة الأحياء'}
      </div>

      {/* معلومات المستخدم */}
      {!collapsed && (
        <div 
          style={{
            padding: '16px',
            borderBottom: '1px solid #001529',
            color: 'rgba(255, 255, 255, 0.65)',
            textAlign: 'center',
          }}
        >
          <div style={{ marginBottom: '8px' }}>
            <UserOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
          </div>
          <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
            {user?.full_name}
          </div>
          <div style={{ fontSize: '12px', opacity: 0.8 }}>
            {user?.role === 'admin' ? 'مدير النظام' : 
             user?.role === 'manager' ? 'مدير' : 'مستخدم'}
          </div>
        </div>
      )}

      {/* القائمة الرئيسية */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{
          border: 'none',
          height: 'calc(100vh - 64px)',
        }}
      />
    </Sider>
  )
}

export default Sidebar
