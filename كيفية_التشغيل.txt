═══════════════════════════════════════════════════════════════
                برنامج شعبة الأحياء - قسم الطبابة العدلية
                         كيفية التشغيل السريع
═══════════════════════════════════════════════════════════════

🚀 للتشغيل السريع:

   Windows:
   ========
   1. اضغط مرتين على ملف: ابدأ_هنا.bat
   
   أو
   
   2. افتح Command Prompt واكتب:
      python start_here.py

   Linux/Mac:
   ==========
   افتح Terminal واكتب:
   python3 start_here.py

───────────────────────────────────────────────────────────────

🔧 إذا لم يعمل البرنامج:

   1. تأكد من تثبيت Python:
      - تحميل من: https://python.org
      - اختر "Add Python to PATH" أثناء التثبيت

   2. شغل التشخيص:
      python diagnose.py

   3. ثبت المتطلبات:
      python install_requirements.py

   4. شغل النسخة البسيطة:
      python simple_start.py

───────────────────────────────────────────────────────────────

🌐 روابط الوصول بعد التشغيل:

   - الخادم: http://localhost:8000
   - وثائق API: http://localhost:8000/docs
   - معلومات النظام: http://localhost:8000/info

───────────────────────────────────────────────────────────────

👤 بيانات تسجيل الدخول:

   المدير:
   --------
   اسم المستخدم: admin
   كلمة المرور: admin123

   المستخدم العادي:
   ----------------
   اسم المستخدم: user
   كلمة المرور: user123

───────────────────────────────────────────────────────────────

📁 الملفات المهمة:

   ابدأ_هنا.bat          - تشغيل سريع (Windows)
   start_here.py          - القائمة الرئيسية
   diagnose.py            - تشخيص المشاكل
   simple_start.py        - تشغيل بسيط
   استكشاف_الأخطاء.md     - دليل حل المشاكل
   README.md              - دليل المشروع الكامل

───────────────────────────────────────────────────────────────

💡 نصائح:

   - ابدأ دائماً بـ start_here.py
   - إذا واجهت مشاكل، شغل diagnose.py
   - للتشغيل البسيط بدون قاعدة بيانات: simple_start.py
   - اقرأ ملف استكشاف_الأخطاء.md للمساعدة

═══════════════════════════════════════════════════════════════
