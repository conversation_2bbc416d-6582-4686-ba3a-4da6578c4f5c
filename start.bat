@echo off
title Forensic Biology Program

echo Starting Forensic Biology Program...
echo.

REM Try py first
py --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found - starting program...
    py server.py
    goto end
)

REM Try python
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Python found - starting program...
    python server.py
    goto end
)

echo ERROR: Python not found
echo.
echo Please install Python from: https://python.org
echo Make sure to check "Add Python to PATH" during installation
echo.

:end
pause
