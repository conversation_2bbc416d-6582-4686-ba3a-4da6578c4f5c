import React from 'react'
import { Layout, Button, Dropdown, Space, Typography, Avatar } from 'antd'
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
} from '@ant-design/icons'
import { useAuth } from '../../contexts/AuthContext'

const { Header: AntHeader } = Layout
const { Text } = Typography

const Header = ({ collapsed, setCollapsed }) => {
  const { user, logout } = useAuth()

  // قائمة المستخدم
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'الملف الشخصي',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'الإعدادات',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'تسجيل الخروج',
      onClick: logout,
    },
  ]

  const handleUserMenuClick = ({ key }) => {
    switch (key) {
      case 'logout':
        logout()
        break
      case 'profile':
        // فتح صفحة الملف الشخصي
        break
      case 'settings':
        // فتح صفحة الإعدادات
        break
      default:
        break
    }
  }

  return (
    <AntHeader 
      className="ant-layout-header"
      style={{
        marginRight: collapsed ? 80 : 250,
        transition: 'margin-right 0.2s',
      }}
    >
      {/* زر طي/فتح القائمة الجانبية */}
      <Button
        type="text"
        icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        onClick={() => setCollapsed(!collapsed)}
        style={{
          fontSize: '16px',
          width: 64,
          height: 64,
          color: 'white',
        }}
      />

      {/* عنوان الصفحة */}
      <div style={{ flex: 1, textAlign: 'center' }}>
        <Text 
          style={{ 
            color: 'white', 
            fontSize: '18px', 
            fontWeight: 'bold' 
          }}
        >
          برنامج شعبة الأحياء - قسم الطبابة العدلية
        </Text>
      </div>

      {/* أدوات الهيدر */}
      <Space size="middle">
        {/* زر الإشعارات */}
        <Button
          type="text"
          icon={<BellOutlined />}
          style={{
            color: 'white',
            fontSize: '16px',
          }}
        />

        {/* قائمة المستخدم */}
        <Dropdown
          menu={{
            items: userMenuItems,
            onClick: handleUserMenuClick,
          }}
          placement="bottomLeft"
          trigger={['click']}
        >
          <Space 
            style={{ 
              cursor: 'pointer',
              padding: '8px 12px',
              borderRadius: '6px',
              transition: 'background-color 0.2s',
            }}
            className="user-dropdown"
          >
            <Avatar 
              icon={<UserOutlined />} 
              style={{ 
                backgroundColor: '#1890ff',
                border: '2px solid white',
              }}
            />
            <div style={{ color: 'white', textAlign: 'right' }}>
              <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                {user?.full_name}
              </div>
              <div style={{ fontSize: '12px', opacity: 0.8 }}>
                {user?.role === 'admin' ? 'مدير النظام' : 
                 user?.role === 'manager' ? 'مدير' : 'مستخدم'}
              </div>
            </div>
          </Space>
        </Dropdown>
      </Space>

      <style jsx>{`
        .user-dropdown:hover {
          background-color: rgba(255, 255, 255, 0.1) !important;
        }
      `}</style>
    </AntHeader>
  )
}

export default Header
