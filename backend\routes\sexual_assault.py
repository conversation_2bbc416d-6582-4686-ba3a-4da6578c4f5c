"""
مسارات الوقوعات الجنسية
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from database import get_db
from models import SexualAssaultCase, User
from routes.auth import get_current_user

router = APIRouter()

class SexualAssaultCaseCreate(BaseModel):
    technical_number: str
    sending_authority: str
    victim_name: str
    victim_age: Optional[int] = None
    victim_gender: Optional[str] = None
    incident_date: Optional[datetime] = None
    exam_date: datetime
    exam_results: Optional[str] = None
    medical_findings: Optional[str] = None
    psychological_notes: Optional[str] = None
    is_confidential: bool = True

class SexualAssaultCaseResponse(BaseModel):
    id: int
    technical_number: str
    sending_authority: str
    victim_name: str
    victim_age: Optional[int]
    victim_gender: Optional[str]
    incident_date: Optional[datetime]
    exam_date: datetime
    exam_results: Optional[str]
    medical_findings: Optional[str]
    psychological_notes: Optional[str]
    is_confidential: bool
    created_at: datetime

    class Config:
        from_attributes = True

@router.get("/", response_model=List[SexualAssaultCaseResponse])
async def get_sexual_assault_cases(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة حالات الوقوعات الجنسية"""
    # التحقق من الصلاحيات للحالات السرية
    if current_user.role not in ["admin", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحية للوصول إلى هذه البيانات"
        )
    
    cases = db.query(SexualAssaultCase).offset(skip).limit(limit).all()
    return cases

@router.post("/", response_model=SexualAssaultCaseResponse)
async def create_sexual_assault_case(
    case_data: SexualAssaultCaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء حالة وقوعة جنسية جديدة"""
    if current_user.role not in ["admin", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحية لإنشاء هذا النوع من الحالات"
        )
    
    new_case = SexualAssaultCase(
        **case_data.dict(),
        created_by=current_user.id
    )
    
    db.add(new_case)
    db.commit()
    db.refresh(new_case)
    
    return new_case
