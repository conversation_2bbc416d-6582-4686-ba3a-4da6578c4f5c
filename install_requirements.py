#!/usr/bin/env python3
"""
تثبيت متطلبات برنامج شعبة الأحياء
"""
import subprocess
import sys
import os

def install_package(package):
    """تثبيت حزمة واحدة"""
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                      check=True, capture_output=True)
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print("📦 تثبيت متطلبات برنامج شعبة الأحياء")
    print("=" * 40)
    
    # المتطلبات الأساسية
    basic_requirements = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'pydantic'
    ]
    
    # المتطلبات الإضافية
    additional_requirements = [
        'python-multipart',
        'python-jose[cryptography]',
        'passlib[bcrypt]',
        'python-docx',
        'jinja2',
        'pandas',
        'python-dateutil',
        'email-validator'
    ]
    
    print("🔧 تثبيت المتطلبات الأساسية...")
    
    failed_basic = []
    for package in basic_requirements:
        print(f"   تثبيت {package}...")
        if install_package(package):
            print(f"   ✅ {package}")
        else:
            print(f"   ❌ {package}")
            failed_basic.append(package)
    
    if failed_basic:
        print(f"\n❌ فشل في تثبيت المتطلبات الأساسية: {failed_basic}")
        print("💡 جرب تشغيل الأمر يدوياً:")
        print(f"   pip install {' '.join(failed_basic)}")
        return
    
    print("\n🔧 تثبيت المتطلبات الإضافية...")
    
    failed_additional = []
    for package in additional_requirements:
        print(f"   تثبيت {package}...")
        if install_package(package):
            print(f"   ✅ {package}")
        else:
            print(f"   ⚠️ {package} (اختياري)")
            failed_additional.append(package)
    
    print("\n" + "=" * 40)
    
    if not failed_basic:
        print("🎉 تم تثبيت المتطلبات الأساسية بنجاح!")
        print("✅ يمكنك الآن تشغيل البرنامج")
        
        if failed_additional:
            print(f"\n⚠️ المتطلبات الاختيارية التي فشلت: {len(failed_additional)}")
            print("💡 البرنامج سيعمل بدونها لكن بميزات محدودة")
        
        print("\n🚀 لتشغيل البرنامج:")
        print("   python simple_start.py")
        print("   أو")
        print("   python quick_start.py")
    else:
        print("❌ فشل في تثبيت المتطلبات الأساسية")
        print("💡 تأكد من اتصالك بالإنترنت وأن pip يعمل بشكل صحيح")

if __name__ == "__main__":
    main()
