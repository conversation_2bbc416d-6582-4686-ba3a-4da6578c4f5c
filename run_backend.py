#!/usr/bin/env python3
"""
سكريبت تشغيل الخادم الخلفي لبرنامج شعبة الأحياء
"""
import os
import sys
import subprocess

def main():
    print("🚀 بدء تشغيل خادم شعبة الأحياء...")
    
    # الانتقال إلى مجلد الخادم
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    os.chdir(backend_dir)
    
    # التحقق من وجود ملف المتطلبات
    if not os.path.exists('requirements.txt'):
        print("❌ ملف requirements.txt غير موجود")
        return
    
    # تثبيت المتطلبات
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True)
        print("✅ تم تثبيت المتطلبات بنجاح")
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return
    
    # إنشاء المستخدمين الافتراضيين
    print("👤 إنشاء المستخدمين الافتراضيين...")
    try:
        subprocess.run([sys.executable, 'create_admin.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"⚠️  تحذير: خطأ في إنشاء المستخدمين: {e}")
    
    # تشغيل الخادم
    print("🌐 بدء تشغيل الخادم...")
    try:
        subprocess.run([sys.executable, 'main.py'], check=True)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    main()
