import React, { useState } from 'react'
import { Form, Input, Button, Card, Typography, Space, Divider } from 'antd'
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

const { Title, Text } = Typography

const Login = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const { login } = useAuth()
  const navigate = useNavigate()

  const handleLogin = async (values) => {
    setLoading(true)
    try {
      const result = await login(values)
      if (result.success) {
        navigate('/dashboard')
      }
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div 
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
      }}
    >
      <Card
        style={{
          width: '100%',
          maxWidth: '400px',
          borderRadius: '20px',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          border: 'none',
        }}
        bodyStyle={{
          padding: '40px',
        }}
      >
        {/* شعار وعنوان التطبيق */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <div 
            style={{
              width: '80px',
              height: '80px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 20px',
              color: 'white',
              fontSize: '32px',
              fontWeight: 'bold',
            }}
          >
            ش.أ
          </div>
          <Title level={2} style={{ margin: 0, color: '#333' }}>
            شعبة الأحياء
          </Title>
          <Text type="secondary" style={{ fontSize: '16px' }}>
            قسم الطبابة العدلية
          </Text>
        </div>

        <Divider />

        {/* نموذج تسجيل الدخول */}
        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="username"
            label="اسم المستخدم"
            rules={[
              { required: true, message: 'يرجى إدخال اسم المستخدم' },
              { min: 3, message: 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="أدخل اسم المستخدم"
              className="neumorphic-input"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="كلمة المرور"
            rules={[
              { required: true, message: 'يرجى إدخال كلمة المرور' },
              { min: 6, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="أدخل كلمة المرور"
              className="neumorphic-input"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<LoginOutlined />}
              block
              style={{
                height: '50px',
                fontSize: '16px',
                fontWeight: 'bold',
                borderRadius: '15px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                boxShadow: '0 8px 16px rgba(102, 126, 234, 0.3)',
              }}
            >
              تسجيل الدخول
            </Button>
          </Form.Item>
        </Form>

        <Divider />

        {/* معلومات إضافية */}
        <div style={{ textAlign: 'center' }}>
          <Space direction="vertical" size="small">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              للحصول على حساب جديد، يرجى التواصل مع مدير النظام
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              الإصدار 1.0.0
            </Text>
          </Space>
        </div>
      </Card>

      {/* معلومات تجريبية */}
      <Card
        style={{
          position: 'absolute',
          top: '20px',
          left: '20px',
          width: '300px',
          borderRadius: '15px',
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(10px)',
        }}
        size="small"
      >
        <Title level={5} style={{ margin: '0 0 10px 0', color: '#333' }}>
          بيانات تجريبية:
        </Title>
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <div>
            <Text strong>المدير:</Text>
            <br />
            <Text code>admin / admin123</Text>
          </div>
          <div>
            <Text strong>مستخدم عادي:</Text>
            <br />
            <Text code>user / user123</Text>
          </div>
        </Space>
      </Card>
    </div>
  )
}

export default Login
