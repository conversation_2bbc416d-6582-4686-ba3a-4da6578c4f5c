# دليل استكشاف الأخطاء - برنامج شعبة الأحياء

## 🔍 خطوات التشخيص السريع

### 1. تشخيص شامل للنظام
```bash
python diagnose.py
```

### 2. تثبيت المتطلبات فقط
```bash
python install_requirements.py
```

### 3. تشغيل بسيط
```bash
python simple_start.py
```

### 4. تشغيل سريع
```bash
python quick_start.py
```

## ❌ المشاكل الشائعة والحلول

### مشكلة: "Python غير موجود"
**الأعراض:**
- رسالة خطأ: `'python' is not recognized`
- البرنامج لا يبدأ

**الحل:**
1. تحميل Python من https://python.org
2. تأكد من تحديد "Add Python to PATH" أثناء التثبيت
3. إعادة تشغيل Command Prompt
4. اختبار: `python --version`

### مشكلة: "pip غير موجود"
**الأعراض:**
- رسالة خطأ: `'pip' is not recognized`
- فشل في تثبيت المتطلبات

**الحل:**
```bash
# Windows
python -m ensurepip --upgrade
python -m pip install --upgrade pip

# Linux/Mac
sudo apt install python3-pip  # Ubuntu/Debian
brew install python3          # Mac
```

### مشكلة: "فشل في تثبيت المتطلبات"
**الأعراض:**
- خطأ أثناء `pip install`
- رسائل خطأ حول المكتبات

**الحل:**
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت المتطلبات الأساسية فقط
pip install fastapi uvicorn sqlalchemy pydantic

# إذا فشل، جرب واحداً تلو الآخر
pip install fastapi
pip install uvicorn
pip install sqlalchemy
pip install pydantic
```

### مشكلة: "خطأ في الاستيراد"
**الأعراض:**
- `ImportError` أو `ModuleNotFoundError`
- البرنامج يتوقف عند بدء التشغيل

**الحل:**
```bash
# تشغيل التشخيص
python diagnose.py

# تثبيت المتطلبات مرة أخرى
python install_requirements.py

# تشغيل الخادم البسيط
python simple_start.py
```

### مشكلة: "المنفذ مستخدم"
**الأعراض:**
- `Address already in use`
- `Port 8000 is already in use`

**الحل:**
```bash
# Windows - إيقاف العملية على المنفذ 8000
netstat -ano | findstr :8000
taskkill /PID <رقم_العملية> /F

# Linux/Mac
lsof -ti:8000 | xargs kill -9

# أو استخدام منفذ آخر
# عدل في الكود: port=8001
```

### مشكلة: "قاعدة البيانات مقفلة"
**الأعراض:**
- `database is locked`
- خطأ في SQLite

**الحل:**
```bash
# حذف قاعدة البيانات وإعادة إنشائها
cd backend
rmdir /s data          # Windows
rm -rf data            # Linux/Mac

# إعادة تشغيل البرنامج
python create_admin.py
```

### مشكلة: "الخادم لا يستجيب"
**الأعراض:**
- البرنامج يبدأ لكن لا يمكن الوصول للموقع
- `Connection refused`

**الحل:**
1. تأكد من أن الخادم يعمل (لا توجد رسائل خطأ)
2. جرب http://127.0.0.1:8000 بدلاً من localhost
3. تأكد من عدم حجب Firewall للمنفذ
4. جرب منفذ آخر

## 🛠️ أدوات التشخيص

### 1. فحص Python
```bash
python --version
python -c "import sys; print(sys.executable)"
```

### 2. فحص pip
```bash
pip --version
pip list
```

### 3. فحص المكتبات
```bash
python -c "import fastapi; print('FastAPI OK')"
python -c "import uvicorn; print('Uvicorn OK')"
python -c "import sqlalchemy; print('SQLAlchemy OK')"
```

### 4. فحص المنافذ
```bash
# Windows
netstat -an | findstr :8000

# Linux/Mac
netstat -an | grep :8000
lsof -i :8000
```

## 🔧 حلول بديلة

### إذا فشل كل شيء - خادم بسيط جداً
```python
# احفظ هذا في ملف test_server.py
from http.server import HTTPServer, SimpleHTTPRequestHandler
import json

class MyHandler(SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        response = {
            "message": "خادم شعبة الأحياء البسيط يعمل",
            "status": "ok"
        }
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

if __name__ == "__main__":
    server = HTTPServer(('localhost', 8000), MyHandler)
    print("خادم بسيط يعمل على http://localhost:8000")
    server.serve_forever()
```

### تشغيل بدون قاعدة بيانات
```python
# خادم بدون قاعدة بيانات - احفظ في minimal_server.py
from fastapi import FastAPI
import uvicorn

app = FastAPI(title="شعبة الأحياء - نسخة بسيطة")

@app.get("/")
def home():
    return {"message": "مرحباً بكم في شعبة الأحياء", "status": "يعمل"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 📞 طلب المساعدة

إذا استمرت المشاكل، يرجى تشغيل:

```bash
python diagnose.py > تقرير_التشخيص.txt
```

وإرسال ملف `تقرير_التشخيص.txt` مع وصف المشكلة.

## 🔄 إعادة التثبيت الكاملة

```bash
# 1. حذف البيئة الافتراضية (إن وجدت)
rmdir /s venv          # Windows
rm -rf venv            # Linux/Mac

# 2. إنشاء بيئة افتراضية جديدة
python -m venv venv

# 3. تفعيل البيئة الافتراضية
venv\Scripts\activate     # Windows
source venv/bin/activate  # Linux/Mac

# 4. تحديث pip
python -m pip install --upgrade pip

# 5. تثبيت المتطلبات
python install_requirements.py

# 6. تشغيل البرنامج
python simple_start.py
```
