import React, { useState, useEffect } from 'react'
import { Row, Col, Card, Statistic, Typography, Space, Spin, Alert } from 'antd'
import {
  UserOutlined,
  ExperimentOutlined,
  TeamOutlined,
  FileTextOutlined,
  TrophyOutlined,
  CalendarOutlined,
} from '@ant-design/icons'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'
import { useQuery } from 'react-query'
import { reportsAPI } from '../services/api'
import { useAuth } from '../contexts/AuthContext'

const { Title, Text } = Typography

const Dashboard = () => {
  const { user } = useAuth()
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)

  // جلب الإحصائيات
  const { data: stats, isLoading, error } = useQuery(
    ['dashboard-stats', selectedYear, selectedMonth],
    () => reportsAPI.getStats({ year: selectedYear, month: selectedMonth }),
    {
      refetchInterval: 30000, // تحديث كل 30 ثانية
    }
  )

  // ألوان الرسوم البيانية
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

  // بيانات الرسم البياني للفئات العمرية
  const ageGroupsData = stats?.data?.age_groups ? 
    Object.entries(stats.data.age_groups).map(([name, value]) => ({
      name,
      value,
    })) : []

  // بيانات الرسم البياني للجهات المرسلة
  const authoritiesData = stats?.data?.authorities ? 
    Object.entries(stats.data.authorities)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([name, value]) => ({
        name: name.length > 20 ? name.substring(0, 20) + '...' : name,
        value,
      })) : []

  if (isLoading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>جاري تحميل البيانات...</div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message="خطأ في تحميل البيانات"
        description="حدث خطأ أثناء تحميل إحصائيات لوحة التحكم"
        type="error"
        showIcon
      />
    )
  }

  return (
    <div>
      {/* ترحيب بالمستخدم */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          مرحباً، {user?.full_name}
        </Title>
        <Text type="secondary" style={{ fontSize: '16px' }}>
          إليك نظرة عامة على أنشطة شعبة الأحياء
        </Text>
      </div>

      {/* بطاقات الإحصائيات الرئيسية */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="neumorphic-card stats-card">
            <Statistic
              title="إجمالي الحالات"
              value={stats?.data?.total_cases || 0}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="neumorphic-card stats-card">
            <Statistic
              title="الحالات الشهرية"
              value={stats?.data?.monthly_cases || 0}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="neumorphic-card stats-card">
            <Statistic
              title="الحالات السنوية"
              value={stats?.data?.yearly_cases || 0}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="neumorphic-card stats-card">
            <Statistic
              title="إجمالي الأطفال"
              value={Object.values(stats?.data?.gender_distribution || {}).reduce((a, b) => a + b, 0)}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#fff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* الرسوم البيانية */}
      <Row gutter={[16, 16]}>
        {/* توزيع الفئات العمرية */}
        <Col xs={24} lg={12}>
          <Card 
            title="توزيع الفئات العمرية" 
            className="neumorphic-card"
            style={{ height: 400 }}
          >
            {ageGroupsData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={ageGroupsData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {ageGroupsData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="empty-container">
                <Text type="secondary">لا توجد بيانات للعرض</Text>
              </div>
            )}
          </Card>
        </Col>

        {/* أهم الجهات المرسلة */}
        <Col xs={24} lg={12}>
          <Card 
            title="أهم الجهات المرسلة" 
            className="neumorphic-card"
            style={{ height: 400 }}
          >
            {authoritiesData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={authoritiesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="name" 
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#1890ff" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="empty-container">
                <Text type="secondary">لا توجد بيانات للعرض</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* توزيع الجنس */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24}>
          <Card title="توزيع الجنس" className="neumorphic-card">
            <Row gutter={[16, 16]}>
              {Object.entries(stats?.data?.gender_distribution || {}).map(([gender, count]) => (
                <Col xs={12} sm={8} md={6} key={gender}>
                  <Card className="neumorphic-card" style={{ textAlign: 'center' }}>
                    <Statistic
                      title={gender}
                      value={count}
                      prefix={gender === 'ذكر' ? <UserOutlined /> : <TeamOutlined />}
                      valueStyle={{ 
                        color: gender === 'ذكر' ? '#1890ff' : '#ff69b4' 
                      }}
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* روابط سريعة */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24}>
          <Card title="روابط سريعة" className="neumorphic-card">
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={8} md={6}>
                <Card 
                  hoverable 
                  className="neumorphic-button"
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.location.href = '/age-estimation'}
                >
                  <UserOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                  <div>تقدير الأعمار</div>
                </Card>
              </Col>
              
              <Col xs={12} sm={8} md={6}>
                <Card 
                  hoverable 
                  className="neumorphic-button"
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.location.href = '/intensity-exam'}
                >
                  <ExperimentOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                  <div>فحص الشدة</div>
                </Card>
              </Col>
              
              <Col xs={12} sm={8} md={6}>
                <Card 
                  hoverable 
                  className="neumorphic-button"
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.location.href = '/gender-determination'}
                >
                  <TeamOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                  <div>تحديد الجنس</div>
                </Card>
              </Col>
              
              <Col xs={12} sm={8} md={6}>
                <Card 
                  hoverable 
                  className="neumorphic-button"
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                  onClick={() => window.location.href = '/reports'}
                >
                  <FileTextOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                  <div>التقارير</div>
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
