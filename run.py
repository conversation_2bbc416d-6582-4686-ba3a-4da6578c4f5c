#!/usr/bin/env python3
"""
ملف التشغيل الرئيسي - أبسط طريقة لتشغيل البرنامج
"""
import sys
import os

def main():
    print("🏥 برنامج شعبة الأحياء - قسم الطبابة العدلية")
    print("=" * 60)
    
    # فحص Python
    version = sys.version_info
    print(f"🐍 Python {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ إصدار Python مناسب")
    print()
    
    # عرض الخيارات
    print("📋 اختر ما تريد فعله:")
    print()
    print("   1️⃣  فحص النظام")
    print("   2️⃣  تشغيل خادم بسيط")
    print("   3️⃣  تثبيت المكتبات")
    print("   4️⃣  مساعدة")
    print("   0️⃣  خروج")
    print()
    
    while True:
        try:
            choice = input("اختر رقماً (0-4): ").strip()
            
            if choice == '0':
                print("👋 شكراً لاستخدام البرنامج")
                break
                
            elif choice == '1':
                print("\n🔍 فحص النظام...")
                run_check()
                
            elif choice == '2':
                print("\n🚀 تشغيل الخادم...")
                run_server()
                
            elif choice == '3':
                print("\n📦 تثبيت المكتبات...")
                install_packages()
                
            elif choice == '4':
                show_help()
                
            else:
                print("❌ اختيار غير صحيح")
                continue
            
            if choice in ['1', '3', '4']:
                input("\nاضغط Enter للعودة للقائمة...")
                print("\n" + "─" * 60 + "\n")
                
        except KeyboardInterrupt:
            print("\n👋 تم إنهاء البرنامج")
            break

def run_check():
    """تشغيل فحص النظام"""
    if os.path.exists('check.py'):
        import subprocess
        subprocess.run([sys.executable, 'check.py'])
    else:
        print("❌ ملف الفحص غير موجود")

def run_server():
    """تشغيل الخادم"""
    if os.path.exists('simple_server.py'):
        import subprocess
        subprocess.run([sys.executable, 'simple_server.py'])
    else:
        print("❌ ملف الخادم غير موجود")

def install_packages():
    """تثبيت المكتبات"""
    print("📦 تثبيت المكتبات الأساسية...")
    
    packages = ['fastapi', 'uvicorn']
    
    for package in packages:
        print(f"تثبيت {package}...")
        try:
            import subprocess
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {package}")
            else:
                print(f"❌ {package}: {result.stderr}")
        except Exception as e:
            print(f"❌ خطأ في تثبيت {package}: {e}")

def show_help():
    """عرض المساعدة"""
    print("\n" + "=" * 60)
    print("📖 مساعدة برنامج شعبة الأحياء")
    print("=" * 60)
    print()
    print("🎯 الهدف:")
    print("   تشغيل برنامج إدارة بيانات شعبة الأحياء")
    print()
    print("📋 الخطوات:")
    print("   1. ابدأ بـ 'فحص النظام' لمعرفة حالة النظام")
    print("   2. إذا احتجت مكتبات، اختر 'تثبيت المكتبات'")
    print("   3. ثم اختر 'تشغيل خادم بسيط'")
    print()
    print("🔗 بعد تشغيل الخادم:")
    print("   - افتح المتصفح على: http://localhost:8000")
    print("   - أو جرب: http://127.0.0.1:8000")
    print()
    print("❌ إذا لم يعمل:")
    print("   1. تأكد من تثبيت Python بشكل صحيح")
    print("   2. جرب تشغيل: python --version")
    print("   3. تأكد من الاتصال بالإنترنت للتثبيت")
    print()
    print("📞 للمساعدة:")
    print("   - شارك نتائج 'فحص النظام'")
    print("   - أرسل أي رسائل خطأ تظهر")
    print()

if __name__ == "__main__":
    main()
