================================================================
                FORENSIC BIOLOGY PROGRAM
                Try These Solutions
================================================================

PROBLEM: "python3.exe not found" error

SOLUTIONS (try in order):

1. Double-click: direct.bat
   (This searches for Python in common locations)

2. Double-click: run_powershell.bat
   (Uses PowerShell instead of Command Prompt)

3. Right-click on run.ps1 → "Run with PowerShell"
   (PowerShell script that tries multiple Python commands)

4. Manual method:
   - Hold Shift + Right-click in this folder
   - Choose "Open PowerShell window here"
   - Type: python server.py
   - Press Enter

================================================================

IF STILL NOT WORKING:

The issue is that Windows is trying to use a broken Python shortcut.

PERMANENT FIX:
1. Go to: Settings → Apps → App execution aliases
2. Turn OFF "python.exe" and "python3.exe"
3. Install Python properly from https://python.org
4. During installation, check "Add Python to PATH"
5. Restart computer

================================================================

QUICK TEST:
1. Open Command Prompt
2. Type: where python
3. Send me the result

================================================================

ALTERNATIVE:
If nothing works, I can create a version that doesn't need Python installation.

================================================================
