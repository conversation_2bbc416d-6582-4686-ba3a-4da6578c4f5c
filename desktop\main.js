const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron')
const path = require('path')
const { spawn } = require('child_process')

// متغيرات عامة
let mainWindow
let backendProcess
const isDev = process.env.NODE_ENV === 'development'

// إنشاء النافذة الرئيسية
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    icon: path.join(__dirname, 'assets', 'icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'default',
    show: false, // لا تظهر النافذة حتى تكون جاهزة
  })

  // تحميل التطبيق
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000')
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadURL('http://localhost:8000')
  }

  // إظهار النافذة عند الجاهزية
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
    
    // التركيز على النافذة
    if (isDev) {
      mainWindow.focus()
    }
  })

  // إغلاق التطبيق عند إغلاق النافذة
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // منع التنقل الخارجي
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    
    if (parsedUrl.origin !== 'http://localhost:3000' && parsedUrl.origin !== 'http://localhost:8000') {
      event.preventDefault()
      shell.openExternal(navigationUrl)
    }
  })

  // منع النوافذ الجديدة
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

// بدء تشغيل الخادم الخلفي
function startBackend() {
  if (isDev) {
    console.log('🔧 وضع التطوير: الخادم يعمل خارجياً')
    return
  }

  try {
    const backendPath = path.join(__dirname, '..', 'backend', 'main.py')
    
    backendProcess = spawn('python', [backendPath], {
      cwd: path.join(__dirname, '..', 'backend'),
      stdio: ['pipe', 'pipe', 'pipe']
    })

    backendProcess.stdout.on('data', (data) => {
      console.log(`🐍 Backend: ${data}`)
    })

    backendProcess.stderr.on('data', (data) => {
      console.error(`❌ Backend Error: ${data}`)
    })

    backendProcess.on('close', (code) => {
      console.log(`🔚 Backend process exited with code ${code}`)
    })

    console.log('🚀 تم بدء تشغيل الخادم الخلفي')
  } catch (error) {
    console.error('❌ خطأ في بدء تشغيل الخادم:', error)
    
    dialog.showErrorBox(
      'خطأ في بدء التشغيل',
      'فشل في بدء تشغيل الخادم الخلفي. تأكد من تثبيت Python والمتطلبات.'
    )
  }
}

// إيقاف الخادم الخلفي
function stopBackend() {
  if (backendProcess) {
    backendProcess.kill()
    backendProcess = null
    console.log('🛑 تم إيقاف الخادم الخلفي')
  }
}

// إنشاء القائمة
function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'إعادة تحميل',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            if (mainWindow) {
              mainWindow.reload()
            }
          }
        },
        {
          label: 'إغلاق',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        {
          label: 'تكبير',
          accelerator: 'CmdOrCtrl+Plus',
          click: () => {
            if (mainWindow) {
              const currentZoom = mainWindow.webContents.getZoomLevel()
              mainWindow.webContents.setZoomLevel(currentZoom + 0.5)
            }
          }
        },
        {
          label: 'تصغير',
          accelerator: 'CmdOrCtrl+-',
          click: () => {
            if (mainWindow) {
              const currentZoom = mainWindow.webContents.getZoomLevel()
              mainWindow.webContents.setZoomLevel(currentZoom - 0.5)
            }
          }
        },
        {
          label: 'الحجم الطبيعي',
          accelerator: 'CmdOrCtrl+0',
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.setZoomLevel(0)
            }
          }
        },
        { type: 'separator' },
        {
          label: 'ملء الشاشة',
          accelerator: 'F11',
          click: () => {
            if (mainWindow) {
              mainWindow.setFullScreen(!mainWindow.isFullScreen())
            }
          }
        }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول البرنامج',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'حول البرنامج',
              message: 'برنامج شعبة الأحياء - قسم الطبابة العدلية',
              detail: 'الإصدار 1.0.0\n\nبرنامج شامل لإدارة بيانات شعبة الأحياء في قسم الطبابة العدلية',
              buttons: ['موافق']
            })
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// أحداث التطبيق
app.whenReady().then(() => {
  console.log('🚀 بدء تشغيل تطبيق شعبة الأحياء')
  
  // بدء الخادم الخلفي
  startBackend()
  
  // انتظار قليل لبدء الخادم
  setTimeout(() => {
    createWindow()
    createMenu()
  }, 3000)

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  stopBackend()
  
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', () => {
  stopBackend()
})

// معالجة الأخطاء
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason)
})

// IPC handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options)
  return result
})

console.log('📱 تطبيق Electron جاهز')
