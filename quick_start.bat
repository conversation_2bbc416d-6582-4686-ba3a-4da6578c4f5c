@echo off
chcp 65001 >nul
title برنامج شعبة الأحياء - قسم الطبابة العدلية

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                برنامج شعبة الأحياء                          ║
echo ║                قسم الطبابة العدلية                          ║
echo ║                    الإصدار 1.0.0                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 فحص النظام...

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo 💡 يرجى تثبيت Python 3.8+ من https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo 📦 تثبيت وإعداد النظام...

REM الانتقال إلى مجلد الخادم
cd backend

REM تثبيت المتطلبات
echo 📥 تثبيت متطلبات Python...
pip install -r requirements.txt >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    echo 💡 تأكد من الاتصال بالإنترنت
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات

echo.
echo 👤 إعداد المستخدمين...
python create_admin.py

echo.
echo 🚀 بدء تشغيل النظام...
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    معلومات الوصول                          ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  🌐 الخادم: http://localhost:8000                          ║
echo ║  📚 وثائق API: http://localhost:8000/api/docs              ║
echo ║                                                              ║
echo ║  👤 بيانات تسجيل الدخول:                                   ║
echo ║     المدير: admin / admin123                               ║
echo ║     المستخدم: user / user123                               ║
echo ║                                                              ║
echo ║  🛑 اضغط Ctrl+C لإيقاف الخادم                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM تشغيل الخادم
python run_server.py

echo.
echo 🛑 تم إيقاف الخادم
pause
