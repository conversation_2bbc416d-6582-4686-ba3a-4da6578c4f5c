import React, { useState } from 'react'
import { Card, Button, Space, Typography, Alert, message, Upload } from 'antd'
import { SettingOutlined, DownloadOutlined, UploadOutlined, LockOutlined } from '@ant-design/icons'
import { useMutation } from 'react-query'
import { settingsAPI } from '../services/api'
import { useAuth } from '../contexts/AuthContext'

const { Title, Text } = Typography

const Settings = () => {
  const { isManager } = useAuth()
  const [loading, setLoading] = useState(false)

  if (!isManager) {
    return (
      <Card className="neumorphic-card">
        <Alert
          message="وصول مقيد"
          description="ليس لديك صلاحية للوصول إلى هذا القسم. هذا القسم مخصص للمديرين فقط."
          type="warning"
          showIcon
          icon={<LockOutlined />}
        />
      </Card>
    )
  }

  const createBackupMutation = useMutation(settingsAPI.createBackup, {
    onSuccess: (data) => {
      message.success(`تم إنشاء النسخة الاحتياطية بنجاح: ${data.data.filename}`)
    },
    onError: () => {
      message.error('خطأ في إنشاء النسخة الاحتياطية')
    },
  })

  const restoreBackupMutation = useMutation(settingsAPI.restoreBackup, {
    onSuccess: () => {
      message.success('تم استعادة النسخة الاحتياطية بنجاح')
    },
    onError: () => {
      message.error('خطأ في استعادة النسخة الاحتياطية')
    },
  })

  const handleBackup = () => {
    createBackupMutation.mutate()
  }

  const handleRestore = (file) => {
    restoreBackupMutation.mutate(file)
    return false // منع الرفع التلقائي
  }

  return (
    <div>
      <Card className="neumorphic-card" style={{ marginBottom: 16 }}>
        <Title level={3} style={{ margin: 0 }}>
          <SettingOutlined style={{ marginLeft: 8 }} />
          الإعدادات
        </Title>
      </Card>

      {/* النسخ الاحتياطي */}
      <Card title="النسخ الاحتياطي" className="neumorphic-card" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>
            يمكنك إنشاء نسخة احتياطية من قاعدة البيانات أو استعادة نسخة احتياطية سابقة.
          </Text>
          
          <Alert
            message="تحذير"
            description="استعادة النسخة الاحتياطية ستحل محل جميع البيانات الحالية. تأكد من إنشاء نسخة احتياطية قبل الاستعادة."
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Space>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleBackup}
              loading={createBackupMutation.isLoading}
            >
              إنشاء نسخة احتياطية
            </Button>

            <Upload
              beforeUpload={handleRestore}
              accept=".db"
              showUploadList={false}
            >
              <Button
                icon={<UploadOutlined />}
                loading={restoreBackupMutation.isLoading}
              >
                استعادة نسخة احتياطية
              </Button>
            </Upload>
          </Space>
        </Space>
      </Card>

      {/* إعدادات النظام */}
      <Card title="إعدادات النظام" className="neumorphic-card" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>إعدادات النظام العامة:</Text>
          
          <div style={{ padding: 20, textAlign: 'center', color: '#999' }}>
            <Text type="secondary">
              إعدادات النظام ستتم إضافتها في التحديثات القادمة
            </Text>
          </div>
        </Space>
      </Card>

      {/* إدارة القوالب */}
      <Card title="إدارة القوالب" className="neumorphic-card">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>إدارة قوالب التقارير:</Text>
          
          <div style={{ padding: 20, textAlign: 'center', color: '#999' }}>
            <Text type="secondary">
              إدارة القوالب ستتم إضافتها في التحديثات القادمة
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  )
}

export default Settings
