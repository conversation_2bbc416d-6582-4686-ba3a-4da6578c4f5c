#!/usr/bin/env python3
"""
تشغيل أبسط نسخة ممكنة من الخادم
"""
import sys
import os

def create_minimal_server():
    """إنشاء أبسط خادم ممكن"""
    
    # جرب FastAPI أولاً
    try:
        from fastapi import FastAPI
        import uvicorn
        
        app = FastAPI(title="شعبة الأحياء")
        
        @app.get("/")
        def home():
            return {
                "message": "برنامج شعبة الأحياء يعمل بنجاح!",
                "status": "OK",
                "server": "FastAPI"
            }
        
        @app.get("/health")
        def health():
            return {"status": "healthy"}
        
        print("✅ تم إنشاء خادم FastAPI")
        return ("fastapi", app)
        
    except ImportError:
        print("⚠️ FastAPI غير متوفر، جرب خادم HTTP بسيط...")
    
    # جرب خادم HTTP بسيط
    try:
        from http.server import HTTPServer, BaseHTTPRequestHandler
        import json
        
        class SimpleHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.end_headers()
                
                response = {
                    "message": "برنامج شعبة الأحياء يعمل بنجاح!",
                    "status": "OK", 
                    "server": "HTTP Basic",
                    "path": self.path
                }
                
                self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
            def log_message(self, format, *args):
                pass  # إخفاء رسائل السجل
        
        print("✅ تم إنشاء خادم HTTP بسيط")
        return ("basic", SimpleHandler)
        
    except Exception as e:
        print(f"❌ فشل في إنشاء خادم HTTP: {e}")
    
    return None, None

def run_server(server_type, server_obj):
    """تشغيل الخادم"""
    
    if server_type == "fastapi":
        print("🚀 بدء تشغيل خادم FastAPI...")
        print("🌐 الخادم متاح على: http://localhost:8000")
        print("📚 وثائق API: http://localhost:8000/docs")
        print("🛑 اضغط Ctrl+C لإيقاف الخادم")
        print("-" * 50)
        
        try:
            import uvicorn
            uvicorn.run(server_obj, host="0.0.0.0", port=8000, log_level="error")
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف الخادم")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل FastAPI: {e}")
    
    elif server_type == "basic":
        print("🚀 بدء تشغيل خادم HTTP بسيط...")
        print("🌐 الخادم متاح على: http://localhost:8000")
        print("🛑 اضغط Ctrl+C لإيقاف الخادم")
        print("-" * 50)
        
        try:
            from http.server import HTTPServer
            server = HTTPServer(('localhost', 8000), server_obj)
            server.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف الخادم")
            server.shutdown()
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل الخادم: {e}")

def main():
    print("🏥 برنامج شعبة الأحياء - تشغيل أساسي")
    print("=" * 45)
    
    # فحص Python
    print(f"🐍 Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # إنشاء الخادم
    server_type, server_obj = create_minimal_server()
    
    if server_type is None:
        print("❌ لا يمكن إنشاء أي خادم")
        print("💡 جرب:")
        print("   1. python install_simple.py")
        print("   2. pip install fastapi uvicorn")
        input("\nاضغط Enter للخروج...")
        return
    
    # تشغيل الخادم
    run_server(server_type, server_obj)

if __name__ == "__main__":
    main()
