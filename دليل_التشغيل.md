# دليل تشغيل برنامج شعبة الأحياء - قسم الطبابة العدلية

## 📋 المتطلبات الأساسية

### 1. Python 3.8 أو أحدث
- تحميل من: https://www.python.org/downloads/
- تأكد من إضافة Python إلى PATH أثناء التثبيت

### 2. Node.js 16 أو أحدث
- تحميل من: https://nodejs.org/
- يتضمن npm تلقائياً

## 🚀 طرق التشغيل

### الطريقة الأولى: التشغيل التلقائي (الأسهل)

#### Windows:
```bash
# تشغيل جميع المكونات
start.bat

# أو تشغيل كل مكون منفصل
start_backend.bat    # الخادم الخلفي
start_frontend.bat   # الواجهة الأمامية
```

#### Linux/Mac:
```bash
# تشغيل الخادم الخلفي
python3 setup.py

# في terminal آخر - تشغيل الواجهة الأمامية
cd frontend
npm install
npm run dev
```

### الطريقة الثانية: التشغيل اليدوي

#### 1. تشغيل الخادم الخلفي
```bash
cd backend
pip install -r requirements.txt
python create_admin.py
python run_server.py
```

#### 2. تشغيل الواجهة الأمامية (في terminal جديد)
```bash
cd frontend
npm install
npm run dev
```

#### 3. تشغيل تطبيق سطح المكتب (اختياري)
```bash
cd desktop
npm install
npm start
```

## 🔗 روابط الوصول

- **الواجهة الأمامية**: http://localhost:3000
- **API الخلفي**: http://localhost:8000
- **وثائق API**: http://localhost:8000/api/docs
- **تطبيق سطح المكتب**: يفتح تلقائياً

## 👤 بيانات تسجيل الدخول

### المدير
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: جميع الصلاحيات

### المستخدم العادي
- **اسم المستخدم**: `user`
- **كلمة المرور**: `user123`
- **الصلاحيات**: محدودة

## 🔧 حل المشاكل الشائعة

### مشكلة: Python غير موجود
```bash
# Windows
# تحميل وتثبيت Python من python.org
# تأكد من تحديد "Add Python to PATH"

# Linux
sudo apt update
sudo apt install python3 python3-pip

# Mac
brew install python3
```

### مشكلة: Node.js غير موجود
```bash
# Windows/Mac
# تحميل وتثبيت من nodejs.org

# Linux
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### مشكلة: خطأ في تثبيت المتطلبات
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت المتطلبات مع إجبار التحديث
pip install -r requirements.txt --upgrade --force-reinstall
```

### مشكلة: المنفذ مستخدم
```bash
# Windows - إيقاف العملية على المنفذ 8000
netstat -ano | findstr :8000
taskkill /PID <PID_NUMBER> /F

# Linux/Mac
lsof -ti:8000 | xargs kill -9
```

### مشكلة: قاعدة البيانات مقفلة
```bash
# حذف قاعدة البيانات وإعادة إنشائها
cd backend
rm -rf data/
python create_admin.py
```

## 📁 هيكل المشروع

```
شعبة الاحياء/
├── backend/                 # الخادم الخلفي (Python/FastAPI)
│   ├── main.py             # الخادم الرئيسي
│   ├── models.py           # نماذج قاعدة البيانات
│   ├── database.py         # إعداد قاعدة البيانات
│   ├── create_admin.py     # إنشاء المستخدمين
│   ├── run_server.py       # تشغيل الخادم
│   ├── routes/             # مسارات API
│   └── requirements.txt    # متطلبات Python
├── frontend/               # الواجهة الأمامية (React)
│   ├── src/
│   ├── package.json
│   └── vite.config.js
├── desktop/                # تطبيق سطح المكتب (Electron)
│   ├── main.js
│   └── package.json
├── start.bat              # تشغيل تلقائي (Windows)
├── start_backend.bat      # تشغيل الخادم (Windows)
├── start_frontend.bat     # تشغيل الواجهة (Windows)
└── setup.py              # إعداد وتشغيل (Python)
```

## 📊 الميزات المتوفرة

### ✅ مكتملة
- تسجيل الدخول والمصادقة
- لوحة التحكم الرئيسية
- إدارة حالات تقدير الأعمار
- إضافة وتعديل وحذف الأطفال
- توليد التقارير المفردة والموحدة
- الإحصائيات والرسوم البيانية
- إدارة المستخدمين (للمديرين)
- النسخ الاحتياطي والاستعادة
- واجهة عربية بتصميم نيومورفيك

### 🔄 قيد التطوير
- نماذج فحص الشدة
- نماذج تحديد الجنس
- نماذج الوقوعات الجنسية
- تحسينات إضافية للتقارير
- المزيد من الإحصائيات

## 📞 الدعم

في حالة وجود مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. تحقق من رسائل الخطأ في Terminal
3. راجع ملف README.md للمزيد من التفاصيل
4. تأكد من عدم استخدام المنافذ من برامج أخرى

## 🔒 الأمان

- تغيير كلمات المرور الافتراضية فور التشغيل
- إنشاء نسخ احتياطية دورية
- تقييد الوصول للبيانات الحساسة
- استخدام HTTPS في البيئة الإنتاجية
