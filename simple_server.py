#!/usr/bin/env python3
"""
خادم بسيط جداً لبرنامج شعبة الأحياء
"""
import http.server
import socketserver
import json
import webbrowser
import threading
import time

class ForensicHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/':
            response = {
                "message": "مرحباً بكم في برنامج شعبة الأحياء - قسم الطبابة العدلية",
                "status": "يعمل بنجاح",
                "version": "1.0.0",
                "server": "Python HTTP Server",
                "endpoints": {
                    "/": "الصفحة الرئيسية",
                    "/info": "معلومات النظام",
                    "/test": "اختبار الاتصال"
                }
            }
        elif self.path == '/info':
            import sys
            import os
            response = {
                "system_info": {
                    "python_version": sys.version,
                    "current_directory": os.getcwd(),
                    "server_type": "HTTP Basic Server"
                },
                "app_info": {
                    "name": "برنامج شعبة الأحياء",
                    "department": "قسم الطبابة العدلية",
                    "version": "1.0.0"
                }
            }
        elif self.path == '/test':
            response = {
                "test": "success",
                "message": "الخادم يعمل بشكل صحيح",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
        else:
            response = {
                "error": "الصفحة غير موجودة",
                "available_paths": ["/", "/info", "/test"]
            }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def log_message(self, format, *args):
        # تعطيل رسائل السجل المزعجة
        pass

def open_browser():
    """فتح المتصفح بعد ثانيتين"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:8000')
        print("🌐 تم فتح المتصفح")
    except:
        print("⚠️ لا يمكن فتح المتصفح تلقائياً")

def main():
    PORT = 8000
    
    print("🏥 برنامج شعبة الأحياء - قسم الطبابة العدلية")
    print("=" * 55)
    print("🚀 بدء تشغيل الخادم البسيط...")
    
    try:
        with socketserver.TCPServer(("", PORT), ForensicHandler) as httpd:
            print(f"✅ الخادم يعمل على: http://localhost:{PORT}")
            print("📋 الصفحات المتاحة:")
            print(f"   🏠 الرئيسية: http://localhost:{PORT}/")
            print(f"   ℹ️  المعلومات: http://localhost:{PORT}/info")
            print(f"   🧪 الاختبار: http://localhost:{PORT}/test")
            print()
            print("🛑 اضغط Ctrl+C لإيقاف الخادم")
            print("=" * 55)
            
            # فتح المتصفح في thread منفصل
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            httpd.serve_forever()
            
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ المنفذ {PORT} مستخدم بالفعل")
            print("💡 جرب:")
            print("   1. إغلاق أي برنامج آخر يستخدم المنفذ")
            print("   2. أو انتظر دقيقة وأعد المحاولة")
        else:
            print(f"❌ خطأ في الشبكة: {e}")
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بنجاح")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
