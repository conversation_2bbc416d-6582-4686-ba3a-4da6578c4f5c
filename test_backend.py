#!/usr/bin/env python3
"""
اختبار سريع للخادم الخلفي
"""
import os
import sys

# إضافة مجلد backend إلى المسار
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🔧 اختبار استيراد الوحدات...")
    
    try:
        from database import init_db, get_db
        print("✅ تم استيراد database بنجاح")
    except Exception as e:
        print(f"❌ خطأ في استيراد database: {e}")
        return False
    
    try:
        from models import User, AgeEstimationCase, Child
        print("✅ تم استيراد models بنجاح")
    except Exception as e:
        print(f"❌ خطأ في استيراد models: {e}")
        return False
    
    try:
        from routes.auth import router as auth_router
        print("✅ تم استيراد auth router بنجاح")
    except Exception as e:
        print(f"⚠️  تحذير: خطأ في استيراد auth router: {e}")
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("🗄️  اختبار قاعدة البيانات...")
    
    try:
        from database import init_db
        init_db()
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_fastapi():
    """اختبار FastAPI"""
    print("🚀 اختبار FastAPI...")
    
    try:
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        
        app = FastAPI(title="اختبار")
        print("✅ تم إنشاء تطبيق FastAPI بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في FastAPI: {e}")
        return False

def main():
    print("🧪 بدء اختبار الخادم الخلفي...")
    print("=" * 50)
    
    # تغيير المجلد الحالي
    os.chdir(backend_path)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("❌ فشل في اختبار الاستيرادات")
        return
    
    # اختبار قاعدة البيانات
    if not test_database():
        print("❌ فشل في اختبار قاعدة البيانات")
        return
    
    # اختبار FastAPI
    if not test_fastapi():
        print("❌ فشل في اختبار FastAPI")
        return
    
    print("=" * 50)
    print("🎉 جميع الاختبارات نجحت!")
    print("✅ الخادم الخلفي جاهز للتشغيل")
    
    # محاولة تشغيل الخادم
    print("\n🚀 محاولة تشغيل الخادم...")
    try:
        import uvicorn
        from main import app
        
        print("🌐 الخادم يعمل على: http://localhost:8000")
        print("📚 وثائق API: http://localhost:8000/api/docs")
        print("🛑 اضغط Ctrl+C لإيقاف الخادم")
        
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    main()
