#!/usr/bin/env python3
"""
سكريبت إعداد وتشغيل برنامج شعبة الأحياء
"""
import os
import sys
import subprocess
import time

def check_python():
    """التحقق من وجود Python"""
    try:
        result = subprocess.run([sys.executable, '--version'], 
                              capture_output=True, text=True)
        print(f"✅ Python متوفر: {result.stdout.strip()}")
        return True
    except Exception as e:
        print(f"❌ Python غير متوفر: {e}")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت متطلبات Python...")
    
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    requirements_file = os.path.join(backend_dir, 'requirements.txt')
    
    if not os.path.exists(requirements_file):
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', requirements_file
        ], check=True, cwd=backend_dir)
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ إعداد قاعدة البيانات...")
    
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    
    try:
        # تشغيل سكريبت إنشاء المستخدمين
        subprocess.run([
            sys.executable, 'create_admin.py'
        ], check=True, cwd=backend_dir)
        print("✅ تم إعداد قاعدة البيانات والمستخدمين")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def start_backend():
    """تشغيل الخادم الخلفي"""
    print("🚀 بدء تشغيل الخادم الخلفي...")
    
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    
    try:
        # تشغيل الخادم في عملية منفصلة
        process = subprocess.Popen([
            sys.executable, 'main.py'
        ], cwd=backend_dir)
        
        print("✅ تم بدء تشغيل الخادم الخلفي")
        print("🌐 الخادم متاح على: http://localhost:8000")
        print("📚 وثائق API: http://localhost:8000/api/docs")
        
        return process
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("🏥 برنامج شعبة الأحياء - قسم الطبابة العدلية")
    print("=" * 60)
    
    # التحقق من Python
    if not check_python():
        print("❌ يرجى تثبيت Python 3.8 أو أحدث")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        return
    
    # إعداد قاعدة البيانات
    if not setup_database():
        print("❌ فشل في إعداد قاعدة البيانات")
        return
    
    # تشغيل الخادم
    backend_process = start_backend()
    if not backend_process:
        print("❌ فشل في تشغيل الخادم")
        return
    
    print("\n" + "=" * 60)
    print("🎉 تم تشغيل البرنامج بنجاح!")
    print("\n📋 معلومات الوصول:")
    print("   - API الخلفي: http://localhost:8000")
    print("   - وثائق API: http://localhost:8000/api/docs")
    print("\n👤 بيانات تسجيل الدخول:")
    print("   - المدير: admin / admin123")
    print("   - المستخدم: user / user123")
    print("\n🛑 اضغط Ctrl+C لإيقاف الخادم")
    print("=" * 60)
    
    try:
        # انتظار إيقاف الخادم
        backend_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 إيقاف الخادم...")
        backend_process.terminate()
        backend_process.wait()
        print("✅ تم إيقاف الخادم بنجاح")

if __name__ == "__main__":
    main()
