import React, { useState, useEffect } from 'react'
import { 
  Form, 
  Input, 
  Button, 
  DatePicker, 
  Select, 
  Table, 
  Space, 
  Card,
  Row,
  Col,
  InputNumber,
  Popconfirm,
  message 
} from 'antd'
import { PlusOutlined, DeleteOutlined, SaveOutlined } from '@ant-design/icons'
import { useMutation, useQueryClient } from 'react-query'
import dayjs from 'dayjs'
import { ageEstimationAPI } from '../../services/api'

const { TextArea } = Input
const { Option } = Select

const AgeEstimationForm = ({ initialValues, onSuccess, onCancel }) => {
  const [form] = Form.useForm()
  const [children, setChildren] = useState([])
  const [loading, setLoading] = useState(false)
  const queryClient = useQueryClient()

  // تحديث البيانات الأولية
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue({
        ...initialValues,
        committee_meeting_date: initialValues.committee_meeting_date ? 
          dayjs(initialValues.committee_meeting_date) : null,
        court_letter_date: initialValues.court_letter_date ? 
          dayjs(initialValues.court_letter_date) : null,
      })
      setChildren(initialValues.children || [])
    } else {
      form.resetFields()
      setChildren([])
    }
  }, [initialValues, form])

  // إنشاء/تحديث حالة
  const saveMutation = useMutation(
    (data) => {
      if (initialValues) {
        return ageEstimationAPI.update(initialValues.id, data)
      } else {
        return ageEstimationAPI.create(data)
      }
    },
    {
      onSuccess: () => {
        message.success(initialValues ? 'تم تحديث الحالة بنجاح' : 'تم إنشاء الحالة بنجاح')
        onSuccess()
      },
      onError: (error) => {
        message.error('خطأ في حفظ البيانات')
        console.error(error)
      },
    }
  )

  // إضافة طفل جديد
  const addChild = () => {
    const newChild = {
      id: Date.now(), // معرف مؤقت
      sequence: children.length + 1,
      name: '',
      gender: '',
      age_numeric: null,
      age_text: '',
      notes: '',
    }
    setChildren([...children, newChild])
  }

  // حذف طفل
  const removeChild = (index) => {
    const newChildren = children.filter((_, i) => i !== index)
    // إعادة ترقيم التسلسل
    const resequencedChildren = newChildren.map((child, i) => ({
      ...child,
      sequence: i + 1,
    }))
    setChildren(resequencedChildren)
  }

  // تحديث بيانات طفل
  const updateChild = (index, field, value) => {
    const newChildren = [...children]
    newChildren[index] = {
      ...newChildren[index],
      [field]: value,
    }
    setChildren(newChildren)
  }

  // حفظ النموذج
  const handleSubmit = async (values) => {
    try {
      setLoading(true)
      
      // التحقق من وجود أطفال
      if (children.length === 0) {
        message.warning('يجب إضافة طفل واحد على الأقل')
        return
      }

      // التحقق من اكتمال بيانات الأطفال
      const incompleteChildren = children.filter(child => !child.name || !child.gender)
      if (incompleteChildren.length > 0) {
        message.warning('يجب إكمال بيانات جميع الأطفال (الاسم والجنس)')
        return
      }

      const formData = {
        ...values,
        committee_meeting_date: values.committee_meeting_date?.toISOString(),
        court_letter_date: values.court_letter_date?.toISOString(),
        children: children.map(child => ({
          sequence: child.sequence,
          name: child.name,
          gender: child.gender,
          age_numeric: child.age_numeric,
          age_text: child.age_text,
          notes: child.notes,
        })),
      }

      await saveMutation.mutateAsync(formData)
    } catch (error) {
      console.error('خطأ في حفظ النموذج:', error)
    } finally {
      setLoading(false)
    }
  }

  // أعمدة جدول الأطفال
  const childrenColumns = [
    {
      title: 'التسلسل',
      dataIndex: 'sequence',
      key: 'sequence',
      width: 80,
      align: 'center',
    },
    {
      title: 'الاسم',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text, record, index) => (
        <Input
          value={text}
          onChange={(e) => updateChild(index, 'name', e.target.value)}
          placeholder="اسم الطفل"
        />
      ),
    },
    {
      title: 'الجنس',
      dataIndex: 'gender',
      key: 'gender',
      width: 100,
      render: (text, record, index) => (
        <Select
          value={text}
          onChange={(value) => updateChild(index, 'gender', value)}
          placeholder="الجنس"
          style={{ width: '100%' }}
        >
          <Option value="ذكر">ذكر</Option>
          <Option value="أنثى">أنثى</Option>
        </Select>
      ),
    },
    {
      title: 'العمر رقماً',
      dataIndex: 'age_numeric',
      key: 'age_numeric',
      width: 120,
      render: (text, record, index) => (
        <InputNumber
          value={text}
          onChange={(value) => updateChild(index, 'age_numeric', value)}
          placeholder="العمر"
          min={0}
          max={100}
          style={{ width: '100%' }}
        />
      ),
    },
    {
      title: 'العمر كتابة',
      dataIndex: 'age_text',
      key: 'age_text',
      width: 150,
      render: (text, record, index) => (
        <Input
          value={text}
          onChange={(e) => updateChild(index, 'age_text', e.target.value)}
          placeholder="العمر كتابة"
        />
      ),
    },
    {
      title: 'الملاحظات',
      dataIndex: 'notes',
      key: 'notes',
      width: 200,
      render: (text, record, index) => (
        <Input
          value={text}
          onChange={(e) => updateChild(index, 'notes', e.target.value)}
          placeholder="ملاحظات"
        />
      ),
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      width: 80,
      align: 'center',
      render: (_, record, index) => (
        <Popconfirm
          title="هل أنت متأكد من حذف هذا الطفل؟"
          onConfirm={() => removeChild(index)}
          okText="نعم"
          cancelText="لا"
        >
          <Button
            type="primary"
            danger
            size="small"
            icon={<DeleteOutlined />}
          />
        </Popconfirm>
      ),
    },
  ]

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      style={{ maxHeight: '70vh', overflowY: 'auto' }}
    >
      {/* معلومات الحالة الأساسية */}
      <Card title="معلومات الحالة" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="technical_number"
              label="الرقم الفني"
              rules={[{ required: true, message: 'يرجى إدخال الرقم الفني' }]}
            >
              <Input placeholder="الرقم الفني" />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="family_head_name"
              label="اسم رب الأسرة"
              rules={[{ required: true, message: 'يرجى إدخال اسم رب الأسرة' }]}
            >
              <Input placeholder="اسم رب الأسرة" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="sending_authority"
              label="الجهة المرسلة"
              rules={[{ required: true, message: 'يرجى إدخال الجهة المرسلة' }]}
            >
              <Input placeholder="الجهة المرسلة" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="court_letter_number"
              label="رقم كتاب المحكمة"
            >
              <Input placeholder="رقم كتاب المحكمة" />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="court_letter_date"
              label="تاريخ كتاب المحكمة"
            >
              <DatePicker 
                style={{ width: '100%' }} 
                placeholder="تاريخ كتاب المحكمة"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="committee_meeting_date"
              label="تاريخ اجتماع اللجنة"
              rules={[{ required: true, message: 'يرجى إدخال تاريخ اجتماع اللجنة' }]}
            >
              <DatePicker 
                style={{ width: '100%' }} 
                placeholder="تاريخ اجتماع اللجنة"
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="notes"
          label="ملاحظات عامة"
        >
          <TextArea 
            rows={3} 
            placeholder="ملاحظات عامة حول الحالة"
          />
        </Form.Item>
      </Card>

      {/* جدول الأطفال */}
      <Card 
        title="بيانات الأطفال" 
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={addChild}
          >
            إضافة طفل
          </Button>
        }
        style={{ marginBottom: 16 }}
      >
        <Table
          columns={childrenColumns}
          dataSource={children}
          rowKey="id"
          pagination={false}
          scroll={{ x: 800 }}
          locale={{
            emptyText: 'لا توجد أطفال. اضغط "إضافة طفل" لإضافة طفل جديد.',
          }}
        />
      </Card>

      {/* أزرار الحفظ والإلغاء */}
      <Form.Item style={{ textAlign: 'center', marginBottom: 0 }}>
        <Space>
          <Button onClick={onCancel}>
            إلغاء
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={<SaveOutlined />}
          >
            {initialValues ? 'تحديث' : 'حفظ'}
          </Button>
        </Space>
      </Form.Item>
    </Form>
  )
}

export default AgeEstimationForm
