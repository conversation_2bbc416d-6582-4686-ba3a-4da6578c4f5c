#!/usr/bin/env python3
"""
نقطة البداية الوحيدة لبرنامج شعبة الأحياء
"""
import sys
import os

def main():
    print("╔" + "═" * 60 + "╗")
    print("║" + "برنامج شعبة الأحياء - قسم الطبابة العدلية".center(60) + "║")
    print("║" + "نقطة البداية الموحدة".center(60) + "║")
    print("╚" + "═" * 60 + "╝")
    print()
    
    # فحص Python
    version = sys.version_info
    print(f"🐍 Python {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ إصدار Python مناسب")
    print()
    
    # عرض الخيارات
    while True:
        print("📋 اختر ما تريد فعله:")
        print()
        print("   1️⃣  اختبار النظام (ابدأ هنا)")
        print("   2️⃣  تثبيت المكتبات الأساسية")
        print("   3️⃣  تشغيل خادم بسيط")
        print("   4️⃣  عرض معلومات المساعدة")
        print("   0️⃣  خروج")
        print()
        
        try:
            choice = input("اختر رقماً (0-4): ").strip()
            
            if choice == '0':
                print("👋 شكراً لاستخدام برنامج شعبة الأحياء")
                break
                
            elif choice == '1':
                print("\n🔍 تشغيل اختبار النظام...")
                run_test()
                
            elif choice == '2':
                print("\n📦 تثبيت المكتبات...")
                run_install()
                
            elif choice == '3':
                print("\n🚀 تشغيل الخادم...")
                run_server()
                
            elif choice == '4':
                show_help()
                
            else:
                print("❌ اختيار غير صحيح")
            
            if choice in ['1', '2', '3']:
                input("\nاضغط Enter للعودة للقائمة...")
                print("\n" + "─" * 60 + "\n")
                
        except KeyboardInterrupt:
            print("\n👋 تم إنهاء البرنامج")
            break

def run_test():
    """تشغيل اختبار النظام"""
    if os.path.exists('test_basic.py'):
        import subprocess
        subprocess.run([sys.executable, 'test_basic.py'])
    else:
        print("❌ ملف الاختبار غير موجود")

def run_install():
    """تشغيل تثبيت المكتبات"""
    if os.path.exists('install_simple.py'):
        import subprocess
        subprocess.run([sys.executable, 'install_simple.py'])
    else:
        print("❌ ملف التثبيت غير موجود")

def run_server():
    """تشغيل الخادم"""
    if os.path.exists('run_basic.py'):
        import subprocess
        subprocess.run([sys.executable, 'run_basic.py'])
    else:
        print("❌ ملف الخادم غير موجود")

def show_help():
    """عرض المساعدة"""
    print("\n" + "═" * 60)
    print("📖 معلومات المساعدة")
    print("═" * 60)
    print()
    print("🎯 الهدف:")
    print("   تشغيل برنامج إدارة بيانات شعبة الأحياء")
    print()
    print("📋 الخطوات المقترحة:")
    print("   1. ابدأ بـ 'اختبار النظام' لمعرفة ما يعمل")
    print("   2. إذا فشل، شغل 'تثبيت المكتبات'")
    print("   3. ثم جرب 'تشغيل خادم بسيط'")
    print()
    print("🔗 روابط مفيدة:")
    print("   - تحميل Python: https://python.org")
    print("   - دليل pip: https://pip.pypa.io")
    print()
    print("📞 للمساعدة:")
    print("   - شارك نتائج 'اختبار النظام'")
    print("   - أرسل أي رسائل خطأ تظهر")
    print()
    print("📁 الملفات المهمة:")
    print("   - START.py (هذا الملف)")
    print("   - test_basic.py (اختبار النظام)")
    print("   - install_simple.py (تثبيت المكتبات)")
    print("   - run_basic.py (تشغيل الخادم)")
    print()

if __name__ == "__main__":
    main()
