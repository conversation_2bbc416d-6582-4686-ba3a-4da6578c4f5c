const { contextBridge, ipcRenderer } = require('electron')

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
  // معلومات التطبيق
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // عرض رسائل
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  
  // معلومات النظام
  platform: process.platform,
  
  // إشعار بأن التطبيق يعمل في Electron
  isElectron: true,
  
  // دوال مساعدة
  log: (message) => console.log('🖥️ Desktop:', message),
  error: (message) => console.error('❌ Desktop Error:', message),
})

// تسجيل أن preload تم تحميله
console.log('🔧 Preload script loaded')

// إضافة معلومات إضافية للنافذة
window.addEventListener('DOMContentLoaded', () => {
  console.log('📄 DOM Content Loaded in Electron')
  
  // إضافة كلاس للجسم للتمييز بين Electron والمتصفح
  document.body.classList.add('electron-app')
  
  // إضافة معلومات النظام
  const systemInfo = {
    platform: process.platform,
    nodeVersion: process.versions.node,
    electronVersion: process.versions.electron,
    chromeVersion: process.versions.chrome
  }
  
  console.log('💻 System Info:', systemInfo)
})
