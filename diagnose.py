#!/usr/bin/env python3
"""
تشخيص مشاكل برنامج شعبة الأحياء
"""
import os
import sys
import subprocess
import importlib.util

def check_python():
    """فحص Python"""
    print("🐍 فحص Python...")
    print(f"   الإصدار: {sys.version}")
    print(f"   المسار: {sys.executable}")
    
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    else:
        print("✅ إصدار Python مناسب")
        return True

def check_pip():
    """فحص pip"""
    print("\n📦 فحص pip...")
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ pip متوفر: {result.stdout.strip()}")
            return True
        else:
            print("❌ pip غير متوفر")
            return False
    except Exception as e:
        print(f"❌ خطأ في pip: {e}")
        return False

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات...")
    
    required_files = [
        'backend/requirements.txt',
        'backend/main.py',
        'backend/database.py',
        'backend/models.py',
        'backend/create_admin.py'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} مفقود")
            missing_files.append(file)
    
    return len(missing_files) == 0

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📥 تثبيت المتطلبات...")
    
    if not os.path.exists('backend/requirements.txt'):
        print("❌ ملف requirements.txt مفقود")
        return False
    
    try:
        # قراءة المتطلبات
        with open('backend/requirements.txt', 'r') as f:
            requirements = f.read().strip().split('\n')
        
        print(f"📋 المتطلبات المطلوبة: {len(requirements)}")
        
        # تثبيت كل متطلب على حدة
        for req in requirements:
            if req.strip() and not req.startswith('#'):
                print(f"   تثبيت {req}...")
                try:
                    subprocess.run([sys.executable, '-m', 'pip', 'install', req], 
                                 check=True, capture_output=True)
                    print(f"   ✅ {req}")
                except subprocess.CalledProcessError as e:
                    print(f"   ❌ فشل في تثبيت {req}: {e}")
        
        print("✅ تم تثبيت المتطلبات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def test_imports():
    """اختبار الاستيرادات"""
    print("\n📚 اختبار الاستيرادات...")
    
    # إضافة مجلد backend للمسار
    backend_path = os.path.join(os.getcwd(), 'backend')
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)
    
    imports_to_test = [
        ('fastapi', 'FastAPI'),
        ('uvicorn', 'uvicorn'),
        ('sqlalchemy', 'SQLAlchemy'),
        ('pydantic', 'Pydantic'),
        ('passlib', 'Passlib'),
        ('jose', 'python-jose'),
    ]
    
    failed_imports = []
    
    for module, name in imports_to_test:
        try:
            importlib.import_module(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} غير متوفر")
            failed_imports.append(name)
    
    # اختبار الوحدات المحلية
    local_modules = ['database', 'models']
    
    for module in local_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module} (محلي)")
        except ImportError as e:
            print(f"❌ {module} (محلي): {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def create_simple_server():
    """إنشاء خادم بسيط للاختبار"""
    print("\n🚀 إنشاء خادم اختبار بسيط...")
    
    simple_server_code = '''
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

app = FastAPI(title="اختبار شعبة الأحياء")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    return {"message": "خادم شعبة الأحياء يعمل بنجاح!", "status": "ok"}

@app.get("/test")
def test_endpoint():
    return {"test": "success", "message": "الاختبار نجح"}

if __name__ == "__main__":
    print("🚀 بدء تشغيل خادم الاختبار...")
    print("🌐 الخادم متاح على: http://localhost:8000")
    print("🛑 اضغط Ctrl+C لإيقاف الخادم")
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    
    # كتابة الخادم البسيط
    with open('backend/simple_server.py', 'w', encoding='utf-8') as f:
        f.write(simple_server_code)
    
    print("✅ تم إنشاء خادم الاختبار: backend/simple_server.py")
    return True

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص مشاكل برنامج شعبة الأحياء")
    print("=" * 50)
    
    # فحص Python
    if not check_python():
        print("\n❌ يرجى تثبيت Python 3.8 أو أحدث")
        return
    
    # فحص pip
    if not check_pip():
        print("\n❌ يرجى تثبيت pip")
        return
    
    # فحص الملفات
    if not check_files():
        print("\n❌ بعض الملفات مفقودة")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("\n❌ فشل في تثبيت المتطلبات")
        return
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ بعض المكتبات غير متوفرة")
        print("💡 جرب: pip install --upgrade -r backend/requirements.txt")
        
        # إنشاء خادم بسيط
        create_simple_server()
        print("\n💡 يمكنك تشغيل خادم بسيط بالأمر:")
        print("   cd backend && python simple_server.py")
        return
    
    print("\n" + "=" * 50)
    print("🎉 جميع الفحوصات نجحت!")
    print("✅ البرنامج جاهز للتشغيل")
    
    print("\n🚀 لتشغيل البرنامج:")
    print("   python quick_start.py")
    print("   أو")
    print("   cd backend && python run_server.py")

if __name__ == "__main__":
    main()
