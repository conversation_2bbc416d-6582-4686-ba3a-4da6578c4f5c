@echo off
echo Forensic Biology Program - Direct Method
echo.

REM Find Python installation
for /f "tokens=*" %%i in ('where python 2^>nul') do (
    echo Found Python at: %%i
    "%%i" server.py
    goto end
)

echo Python not found in PATH
echo Trying common installation locations...

if exist "C:\Python39\python.exe" (
    echo Found Python 3.9
    "C:\Python39\python.exe" server.py
    goto end
)

if exist "C:\Python310\python.exe" (
    echo Found Python 3.10
    "C:\Python310\python.exe" server.py
    goto end
)

if exist "C:\Python311\python.exe" (
    echo Found Python 3.11
    "C:\Python311\python.exe" server.py
    goto end
)

if exist "C:\Python312\python.exe" (
    echo Found Python 3.12
    "C:\Python312\python.exe" server.py
    goto end
)

REM Try AppData locations
if exist "%LOCALAPPDATA%\Programs\Python\Python39\python.exe" (
    echo Found Python 3.9 in AppData
    "%LOCALAPPDATA%\Programs\Python\Python39\python.exe" server.py
    goto end
)

if exist "%LOCALAPPDATA%\Programs\Python\Python310\python.exe" (
    echo Found Python 3.10 in AppData
    "%LOCALAPPDATA%\Programs\Python\Python310\python.exe" server.py
    goto end
)

if exist "%LOCALAPPDATA%\Programs\Python\Python311\python.exe" (
    echo Found Python 3.11 in AppData
    "%LOCALAPPDATA%\Programs\Python\Python311\python.exe" server.py
    goto end
)

if exist "%LOCALAPPDATA%\Programs\Python\Python312\python.exe" (
    echo Found Python 3.12 in AppData
    "%LOCALAPPDATA%\Programs\Python\Python312\python.exe" server.py
    goto end
)

echo ERROR: Could not find Python installation
echo.
echo Please install Python from: https://python.org/downloads/
echo Make sure to check "Add Python to PATH" during installation
echo.

:end
pause
