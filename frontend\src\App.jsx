import React, { useState, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout, message } from 'antd'
import Sidebar from './components/Layout/Sidebar'
import Header from './components/Layout/Header'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import AgeEstimation from './pages/AgeEstimation'
import IntensityExam from './pages/IntensityExam'
import GenderDetermination from './pages/GenderDetermination'
import SexualAssault from './pages/SexualAssault'
import Reports from './pages/Reports'
import Users from './pages/Users'
import Settings from './pages/Settings'
import { AuthProvider, useAuth } from './contexts/AuthContext'

const { Content } = Layout

// مكون الحماية للمسارات
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth()
  
  if (loading) {
    return <div className="loading-container">جاري التحميل...</div>
  }
  
  if (!user) {
    return <Navigate to="/login" replace />
  }
  
  return children
}

// مكون التطبيق الرئيسي
const AppContent = () => {
  const { user } = useAuth()
  const [collapsed, setCollapsed] = useState(false)

  if (!user) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    )
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sidebar collapsed={collapsed} />
      <Layout>
        <Header 
          collapsed={collapsed} 
          setCollapsed={setCollapsed}
        />
        <Content className="ant-layout-content">
          <Routes>
            <Route path="/" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />
            <Route path="/age-estimation" element={
              <ProtectedRoute>
                <AgeEstimation />
              </ProtectedRoute>
            } />
            <Route path="/intensity-exam" element={
              <ProtectedRoute>
                <IntensityExam />
              </ProtectedRoute>
            } />
            <Route path="/gender-determination" element={
              <ProtectedRoute>
                <GenderDetermination />
              </ProtectedRoute>
            } />
            <Route path="/sexual-assault" element={
              <ProtectedRoute>
                <SexualAssault />
              </ProtectedRoute>
            } />
            <Route path="/reports" element={
              <ProtectedRoute>
                <Reports />
              </ProtectedRoute>
            } />
            <Route path="/users" element={
              <ProtectedRoute>
                <Users />
              </ProtectedRoute>
            } />
            <Route path="/settings" element={
              <ProtectedRoute>
                <Settings />
              </ProtectedRoute>
            } />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  )
}

// التطبيق الرئيسي مع مزود السياق
const App = () => {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  )
}

export default App
