"""
مسارات الإعدادات
"""
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
import shutil
import os
from datetime import datetime

from database import get_db
from models import SystemSettings, User
from routes.auth import get_current_user

router = APIRouter()

class SettingCreate(BaseModel):
    key: str
    value: str
    description: Optional[str] = None

class SettingResponse(BaseModel):
    id: int
    key: str
    value: str
    description: Optional[str]
    updated_at: datetime

    class Config:
        from_attributes = True

@router.get("/", response_model=List[SettingResponse])
async def get_settings(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على الإعدادات"""
    if current_user.role not in ["admin", "manager"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحية لعرض الإعدادات"
        )
    
    settings = db.query(SystemSettings).all()
    return settings

@router.post("/", response_model=SettingResponse)
async def create_setting(
    setting_data: SettingCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء إعداد جديد"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحية لإنشاء الإعدادات"
        )
    
    setting = SystemSettings(
        key=setting_data.key,
        value=setting_data.value,
        description=setting_data.description,
        updated_by=current_user.id
    )
    
    db.add(setting)
    db.commit()
    db.refresh(setting)
    
    return setting

@router.put("/{setting_id}", response_model=SettingResponse)
async def update_setting(
    setting_id: int,
    setting_data: SettingCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """تحديث إعداد"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحية لتعديل الإعدادات"
        )
    
    setting = db.query(SystemSettings).filter(SystemSettings.id == setting_id).first()
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الإعداد غير موجود"
        )
    
    setting.value = setting_data.value
    setting.description = setting_data.description
    setting.updated_by = current_user.id
    
    db.commit()
    db.refresh(setting)
    
    return setting

@router.post("/backup")
async def create_backup(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء نسخة احتياطية"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحية لإنشاء النسخ الاحتياطية"
        )
    
    # إنشاء نسخة احتياطية من قاعدة البيانات
    backup_filename = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    backup_path = f"data/backups/{backup_filename}"
    
    try:
        shutil.copy2("data/forensic_biology.db", backup_path)
        return {"message": "تم إنشاء النسخة الاحتياطية بنجاح", "filename": backup_filename}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
        )

@router.post("/restore")
async def restore_backup(
    backup_file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """استعادة نسخة احتياطية"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحية لاستعادة النسخ الاحتياطية"
        )
    
    try:
        # حفظ الملف المرفوع
        with open("data/temp_restore.db", "wb") as buffer:
            shutil.copyfileobj(backup_file.file, buffer)
        
        # استبدال قاعدة البيانات الحالية
        shutil.move("data/temp_restore.db", "data/forensic_biology.db")
        
        return {"message": "تم استعادة النسخة الاحتياطية بنجاح"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"
        )

@router.get("/backups")
async def list_backups(
    current_user: User = Depends(get_current_user)
):
    """قائمة النسخ الاحتياطية"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="ليس لديك صلاحية لعرض النسخ الاحتياطية"
        )
    
    backup_dir = "data/backups"
    if not os.path.exists(backup_dir):
        return {"backups": []}
    
    backups = []
    for filename in os.listdir(backup_dir):
        if filename.endswith('.db'):
            file_path = os.path.join(backup_dir, filename)
            file_stats = os.stat(file_path)
            backups.append({
                "filename": filename,
                "size": file_stats.st_size,
                "created_at": datetime.fromtimestamp(file_stats.st_ctime).isoformat()
            })
    
    return {"backups": sorted(backups, key=lambda x: x['created_at'], reverse=True)}
