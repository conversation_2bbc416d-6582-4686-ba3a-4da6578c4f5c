import React, { useState } from 'react'
import { Card, Table, Button, Space, Input, Modal, Typography, message, Alert } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, HeartOutlined, LockOutlined } from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import dayjs from 'dayjs'
import { sexualAssaultAPI } from '../services/api'
import { useAuth } from '../contexts/AuthContext'

const { Title } = Typography
const { Search } = Input

const SexualAssault = () => {
  const [searchText, setSearchText] = useState('')
  const [isModalVisible, setIsModalVisible] = useState(false)
  const { isManager } = useAuth()
  const queryClient = useQueryClient()

  if (!isManager) {
    return (
      <Card className="neumorphic-card">
        <Alert
          message="وصول مقيد"
          description="ليس لديك صلاحية للوصول إلى هذا القسم. هذا القسم مخصص للمديرين فقط."
          type="warning"
          showIcon
          icon={<LockOutlined />}
        />
      </Card>
    )
  }

  const { data: cases, isLoading } = useQuery(
    ['sexual-assault-cases', searchText],
    () => sexualAssaultAPI.getAll({ search: searchText })
  )

  const deleteMutation = useMutation(sexualAssaultAPI.delete, {
    onSuccess: () => {
      message.success('تم حذف الحالة بنجاح')
      queryClient.invalidateQueries('sexual-assault-cases')
    },
  })

  const columns = [
    {
      title: 'الرقم الفني',
      dataIndex: 'technical_number',
      key: 'technical_number',
    },
    {
      title: 'اسم الضحية',
      dataIndex: 'victim_name',
      key: 'victim_name',
      render: (text) => '***' // إخفاء الاسم للخصوصية
    },
    {
      title: 'الجهة المرسلة',
      dataIndex: 'sending_authority',
      key: 'sending_authority',
    },
    {
      title: 'تاريخ الفحص',
      dataIndex: 'exam_date',
      key: 'exam_date',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: 'سرية',
      dataIndex: 'is_confidential',
      key: 'is_confidential',
      render: (isConfidential) => isConfidential ? <LockOutlined style={{ color: 'red' }} /> : null,
    },
    {
      title: 'الإجراءات',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="primary" size="small" icon={<EditOutlined />} />
          <Button 
            type="primary" 
            danger 
            size="small" 
            icon={<DeleteOutlined />}
            onClick={() => deleteMutation.mutate(record.id)}
          />
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Alert
        message="قسم سري"
        description="هذا القسم يحتوي على معلومات حساسة وسرية. يرجى التعامل مع البيانات بحذر شديد."
        type="error"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Card className="neumorphic-card" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            <HeartOutlined style={{ marginLeft: 8 }} />
            الوقوعات الجنسية
          </Title>
          
          <Space>
            <Search
              placeholder="البحث في الحالات..."
              allowClear
              style={{ width: 300 }}
              onSearch={setSearchText}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsModalVisible(true)}
            >
              إضافة حالة جديدة
            </Button>
          </Space>
        </div>
      </Card>

      <Card className="neumorphic-card">
        <Table
          columns={columns}
          dataSource={cases?.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} من ${total} حالة`,
          }}
        />
      </Card>

      <Modal
        title="إضافة حالة وقوعة جنسية جديدة"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ padding: 20, textAlign: 'center' }}>
          <p>نموذج الوقوعات الجنسية سيتم إضافته قريباً</p>
        </div>
      </Modal>
    </div>
  )
}

export default SexualAssault
