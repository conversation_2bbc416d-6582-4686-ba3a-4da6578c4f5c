#!/usr/bin/env python3
"""
Simplest possible server for Forensic Biology Program
"""
import http.server
import socketserver
import json

class Handler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        
        response = {
            "message": "Forensic Biology Program is running!",
            "arabic": "برنامج شعبة الأحياء يعمل بنجاح!",
            "status": "OK"
        }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def log_message(self, format, *args):
        pass

PORT = 8000

print("Starting Forensic Biology Program...")
print(f"Server will run at: http://localhost:{PORT}")
print("Press Ctrl+C to stop")

try:
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        httpd.serve_forever()
except KeyboardInterrupt:
    print("\nServer stopped")
except Exception as e:
    print(f"Error: {e}")

input("Press Enter to exit...")
