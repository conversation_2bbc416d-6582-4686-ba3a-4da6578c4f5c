#!/usr/bin/env python3
"""
Simple server for Forensic Biology Program
"""
import http.server
import socketserver
import json
import webbrowser
import threading
import time
import sys

def open_browser():
    """Open browser after 2 seconds"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:8000')
        print("<PERSON><PERSON><PERSON> opened automatically")
    except:
        print("Could not open browser automatically")

class ForensicHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if self.path == '/':
            response = {
                "message": "Welcome to Forensic Biology Program",
                "arabic_message": "مرحباً بكم في برنامج شعبة الأحياء - قسم الطبابة العدلية",
                "status": "Running Successfully",
                "version": "1.0.0",
                "server": "Python HTTP Server",
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}",
                "endpoints": {
                    "/": "Home page",
                    "/info": "System information", 
                    "/test": "Connection test"
                }
            }
        elif self.path == '/info':
            import os
            response = {
                "system_info": {
                    "python_version": sys.version,
                    "current_directory": os.getcwd(),
                    "server_type": "HTTP Basic Server"
                },
                "app_info": {
                    "name": "Forensic Biology Program",
                    "arabic_name": "برنامج شعبة الأحياء",
                    "department": "Forensic Medicine Department",
                    "arabic_department": "قسم الطبابة العدلية",
                    "version": "1.0.0"
                }
            }
        elif self.path == '/test':
            response = {
                "test": "SUCCESS",
                "message": "Server is working correctly",
                "arabic_message": "الخادم يعمل بشكل صحيح",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
        else:
            response = {
                "error": "Page not found",
                "arabic_error": "الصفحة غير موجودة",
                "available_paths": ["/", "/info", "/test"]
            }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def log_message(self, format, *args):
        # Disable verbose logging
        pass

def main():
    PORT = 8000
    
    print("=" * 60)
    print("FORENSIC BIOLOGY PROGRAM")
    print("Forensic Medicine Department")
    print("=" * 60)
    print("Starting server...")
    
    try:
        with socketserver.TCPServer(("", PORT), ForensicHandler) as httpd:
            print(f"SUCCESS: Server running at http://localhost:{PORT}")
            print("Available pages:")
            print(f"  Home: http://localhost:{PORT}/")
            print(f"  Info: http://localhost:{PORT}/info")
            print(f"  Test: http://localhost:{PORT}/test")
            print()
            print("Browser will open automatically...")
            print("Press Ctrl+C to stop the server")
            print("=" * 60)
            
            # Open browser in separate thread
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            httpd.serve_forever()
            
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"ERROR: Port {PORT} is already in use")
            print("Solution: Wait 1 minute and try again")
            print("Or close any other program using port 8000")
        else:
            print(f"ERROR: Network error - {e}")
    except KeyboardInterrupt:
        print("\nServer stopped successfully")
    except Exception as e:
        print(f"ERROR: Unexpected error - {e}")
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    main()
