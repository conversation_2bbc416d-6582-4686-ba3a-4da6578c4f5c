#!/usr/bin/env python3
"""
فحص بسيط جداً لمعرفة المشكلة
"""
import sys
import os

print("=" * 50)
print("فحص بسيط لبرنامج شعبة الأحياء")
print("=" * 50)

# 1. معلومات Python
print(f"Python Version: {sys.version}")
print(f"Python Path: {sys.executable}")
print(f"Current Directory: {os.getcwd()}")

# 2. فحص الملفات
print("\nالملفات الموجودة:")
files = os.listdir('.')
for f in sorted(files):
    if f.endswith('.py'):
        print(f"  ✓ {f}")

# 3. اختبار استيراد بسيط
print("\nاختبار الاستيرادات:")
try:
    import json
    print("  ✓ json")
except:
    print("  ✗ json")

try:
    import http.server
    print("  ✓ http.server")
except:
    print("  ✗ http.server")

try:
    import fastapi
    print("  ✓ fastapi")
except:
    print("  ✗ fastapi (غير مثبت)")

try:
    import uvicorn
    print("  ✓ uvicorn")
except:
    print("  ✗ uvicorn (غير مثبت)")

# 4. إنشاء خادم بسيط جداً
print("\nإنشاء خادم بسيط...")

server_code = '''
import http.server
import socketserver
import json

class MyHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        
        response = {
            "message": "برنامج شعبة الأحياء يعمل!",
            "status": "OK"
        }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

if __name__ == "__main__":
    PORT = 8000
    with socketserver.TCPServer(("", PORT), MyHandler) as httpd:
        print(f"Server running at http://localhost:{PORT}")
        print("Press Ctrl+C to stop")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\\nServer stopped")
'''

with open('simple_server.py', 'w', encoding='utf-8') as f:
    f.write(server_code)

print("تم إنشاء simple_server.py")

print("\n" + "=" * 50)
print("التوصيات:")
print("1. لتشغيل خادم بسيط: python simple_server.py")
print("2. ثم افتح: http://localhost:8000")
print("3. شارك نتائج هذا الفحص معي")
print("=" * 50)

input("اضغط Enter للخروج...")
