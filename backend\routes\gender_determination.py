"""
مسارات تحديد الجنس
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from database import get_db
from models import GenderDeterminationCase, User
from routes.auth import get_current_user

router = APIRouter()

class GenderDeterminationCaseCreate(BaseModel):
    technical_number: str
    sending_authority: str
    subject_name: str
    subject_age: Optional[int] = None
    exam_date: datetime
    determination_result: Optional[str] = None
    exam_method: Optional[str] = None
    medical_notes: Optional[str] = None

class GenderDeterminationCaseResponse(BaseModel):
    id: int
    technical_number: str
    sending_authority: str
    subject_name: str
    subject_age: Optional[int]
    exam_date: datetime
    determination_result: Optional[str]
    exam_method: Optional[str]
    medical_notes: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True

@router.get("/", response_model=List[GenderDeterminationCaseResponse])
async def get_gender_determination_cases(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة حالات تحديد الجنس"""
    cases = db.query(GenderDeterminationCase).offset(skip).limit(limit).all()
    return cases

@router.post("/", response_model=GenderDeterminationCaseResponse)
async def create_gender_determination_case(
    case_data: GenderDeterminationCaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء حالة تحديد جنس جديدة"""
    new_case = GenderDeterminationCase(
        **case_data.dict(),
        created_by=current_user.id
    )
    
    db.add(new_case)
    db.commit()
    db.refresh(new_case)
    
    return new_case
