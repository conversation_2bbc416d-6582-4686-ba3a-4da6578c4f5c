#!/usr/bin/env python3
"""
تشغيل النظام الكامل - الخادم والواجهة الأمامية
"""
import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔧 فحص المتطلبات...")
    
    # فحص Python
    if sys.version_info < (3, 8):
        print(f"❌ Python {sys.version_info.major}.{sys.version_info.minor} - يتطلب 3.8+")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # فحص Node.js
    try:
        result = subprocess.run(['node', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Node.js {result.stdout.strip()}")
            return True
        else:
            print("⚠️ Node.js غير متوفر - ستعمل API فقط")
            return "backend_only"
    except:
        print("⚠️ Node.js غير متوفر - ستعمل API فقط")
        return "backend_only"

def setup_backend():
    """إعداد الخادم الخلفي"""
    print("📦 إعداد الخادم الخلفي...")
    
    backend_dir = Path(__file__).parent / 'backend'
    
    # تثبيت المتطلبات
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], check=True, cwd=backend_dir, capture_output=True)
        print("✅ تم تثبيت متطلبات Python")
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت متطلبات Python")
        return False
    
    # إعداد قاعدة البيانات
    try:
        subprocess.run([
            sys.executable, 'create_admin.py'
        ], check=True, cwd=backend_dir, capture_output=True)
        print("✅ تم إعداد قاعدة البيانات")
    except subprocess.CalledProcessError:
        print("⚠️ تحذير: مشكلة في إعداد قاعدة البيانات")
    
    return True

def setup_frontend():
    """إعداد الواجهة الأمامية"""
    print("📦 إعداد الواجهة الأمامية...")
    
    frontend_dir = Path(__file__).parent / 'frontend'
    
    if not frontend_dir.exists():
        print("⚠️ مجلد الواجهة الأمامية غير موجود")
        return False
    
    try:
        subprocess.run(['npm', 'install'], 
                      check=True, cwd=frontend_dir, capture_output=True)
        print("✅ تم تثبيت متطلبات Node.js")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت متطلبات Node.js")
        return False

def start_backend():
    """تشغيل الخادم الخلفي"""
    print("🚀 بدء تشغيل الخادم الخلفي...")
    
    backend_dir = Path(__file__).parent / 'backend'
    
    try:
        process = subprocess.Popen([
            sys.executable, 'run_server.py'
        ], cwd=backend_dir)
        
        # انتظار بدء الخادم
        time.sleep(3)
        print("✅ تم بدء تشغيل الخادم الخلفي")
        return process
    except Exception as e:
        print(f"❌ فشل في تشغيل الخادم: {e}")
        return None

def start_frontend():
    """تشغيل الواجهة الأمامية"""
    print("🌐 بدء تشغيل الواجهة الأمامية...")
    
    frontend_dir = Path(__file__).parent / 'frontend'
    
    try:
        process = subprocess.Popen(['npm', 'run', 'dev'], 
                                 cwd=frontend_dir)
        
        # انتظار بدء الواجهة
        time.sleep(5)
        print("✅ تم بدء تشغيل الواجهة الأمامية")
        return process
    except Exception as e:
        print(f"❌ فشل في تشغيل الواجهة: {e}")
        return None

def open_browser():
    """فتح المتصفح"""
    print("🌐 فتح المتصفح...")
    try:
        webbrowser.open('http://localhost:3000')
    except:
        print("⚠️ لا يمكن فتح المتصفح تلقائياً")

def main():
    """الدالة الرئيسية"""
    print("🏥 برنامج شعبة الأحياء - قسم الطبابة العدلية")
    print("=" * 60)
    
    # فحص المتطلبات
    requirements_check = check_requirements()
    if requirements_check == False:
        print("❌ المتطلبات غير مكتملة")
        return
    
    # إعداد الخادم الخلفي
    if not setup_backend():
        print("❌ فشل في إعداد الخادم الخلفي")
        return
    
    # إعداد الواجهة الأمامية (إذا كان Node.js متوفراً)
    frontend_available = False
    if requirements_check == True:
        frontend_available = setup_frontend()
    
    # تشغيل الخادم الخلفي
    backend_process = start_backend()
    if not backend_process:
        print("❌ فشل في تشغيل الخادم الخلفي")
        return
    
    # تشغيل الواجهة الأمامية (إذا كانت متوفرة)
    frontend_process = None
    if frontend_available:
        frontend_process = start_frontend()
        if frontend_process:
            time.sleep(3)
            open_browser()
    
    # عرض معلومات الوصول
    print("\n" + "=" * 60)
    print("🎉 تم تشغيل النظام بنجاح!")
    print("\n📋 معلومات الوصول:")
    print("   - API الخلفي: http://localhost:8000")
    print("   - وثائق API: http://localhost:8000/api/docs")
    
    if frontend_process:
        print("   - الواجهة الأمامية: http://localhost:3000")
    else:
        print("   - الواجهة الأمامية: غير متوفرة")
    
    print("\n👤 بيانات تسجيل الدخول:")
    print("   - المدير: admin / admin123")
    print("   - المستخدم: user / user123")
    print("\n🛑 اضغط Ctrl+C لإيقاف النظام")
    print("=" * 60)
    
    try:
        # انتظار إيقاف النظام
        if frontend_process:
            frontend_process.wait()
        else:
            backend_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 إيقاف النظام...")
        
        if frontend_process:
            frontend_process.terminate()
            frontend_process.wait()
        
        backend_process.terminate()
        backend_process.wait()
        
        print("✅ تم إيقاف النظام بنجاح")

if __name__ == "__main__":
    main()
