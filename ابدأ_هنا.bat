@echo off
chcp 65001 >nul
title برنامج شعبة الأحياء - قسم الطبابة العدلية

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                برنامج شعبة الأحياء                          ║
echo ║                قسم الطبابة العدلية                          ║
echo ║                    الإصدار 1.0.0                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 فحص النظام...

REM التحقق من Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت
    echo.
    echo 💡 يرجى تثبيت Python من الرابط التالي:
    echo    https://www.python.org/downloads/
    echo.
    echo ⚠️  تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo 🚀 بدء تشغيل البرنامج...
python start_here.py

echo.
echo 👋 شكراً لاستخدام برنامج شعبة الأحياء
pause
