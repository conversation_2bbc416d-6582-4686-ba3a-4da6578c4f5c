#!/usr/bin/env python3
"""
تشغيل بسيط جداً لبرنامج شعبة الأحياء
"""
import os
import sys

# إضافة مجلد backend للمسار
backend_path = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_path)

def create_basic_server():
    """إنشاء خادم أساسي"""
    try:
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        import uvicorn
        
        app = FastAPI(
            title="برنامج شعبة الأحياء",
            description="نظام إدارة بيانات شعبة الأحياء - قسم الطبابة العدلية",
            version="1.0.0"
        )
        
        # إعداد CORS
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        @app.get("/")
        def home():
            return {
                "message": "مرحباً بكم في برنامج شعبة الأحياء - قسم الطبابة العدلية",
                "status": "يعمل بنجاح",
                "version": "1.0.0",
                "endpoints": {
                    "الصفحة الرئيسية": "/",
                    "فحص الصحة": "/health",
                    "وثائق API": "/docs"
                }
            }
        
        @app.get("/health")
        def health():
            return {
                "status": "healthy",
                "message": "الخادم يعمل بشكل طبيعي"
            }
        
        @app.get("/info")
        def info():
            return {
                "app_name": "برنامج شعبة الأحياء",
                "department": "قسم الطبابة العدلية",
                "version": "1.0.0",
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "features": [
                    "تقدير الأعمار",
                    "فحص الشدة", 
                    "تحديد الجنس",
                    "الوقوعات الجنسية",
                    "التقارير والإحصائيات"
                ]
            }
        
        return app
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 يرجى تثبيت المتطلبات أولاً:")
        print("   pip install fastapi uvicorn")
        return None

def main():
    print("🏥 برنامج شعبة الأحياء - تشغيل بسيط")
    print("=" * 45)
    
    # التحقق من Python
    print(f"🐍 Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return
    
    # إنشاء الخادم
    app = create_basic_server()
    if not app:
        return
    
    print("✅ تم إنشاء الخادم بنجاح")
    print("\n🚀 بدء تشغيل الخادم...")
    print("🌐 الخادم متاح على: http://localhost:8000")
    print("📚 وثائق API: http://localhost:8000/docs")
    print("ℹ️  معلومات النظام: http://localhost:8000/info")
    print("🛑 اضغط Ctrl+C لإيقاف الخادم")
    print("-" * 45)
    
    try:
        import uvicorn
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بنجاح")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    main()
