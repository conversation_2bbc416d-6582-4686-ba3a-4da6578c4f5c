═══════════════════════════════════════════════════════════════
                برنامج شعبة الأحياء - قسم الطبابة العدلية
                         كيف أشغل البرنامج؟
═══════════════════════════════════════════════════════════════

🚀 الطريقة الأسهل:

   1. اضغط مرتين على: run.py
   
   2. أو افتح Command Prompt واكتب:
      python run.py

   3. اختر من القائمة:
      - اختر "1" لفحص النظام
      - اختر "2" لتشغيل الخادم
      - اختر "3" لتثبيت المكتبات

───────────────────────────────────────────────────────────────

❌ إذا لم يعمل:

   المشكلة: "python غير موجود"
   الحل:
   1. تحميل Python من: https://python.org
   2. أثناء التثبيت، اختر "Add Python to PATH"
   3. إعادة تشغيل الكمبيوتر
   4. فتح Command Prompt جديد
   5. كتابة: python --version

───────────────────────────────────────────────────────────────

🔍 للتشخيص:

   اكتب في Command Prompt:
   python check.py
   
   وشارك النتائج معي

───────────────────────────────────────────────────────────────

🌐 بعد تشغيل الخادم:

   افتح المتصفح على:
   http://localhost:8000

───────────────────────────────────────────────────────────────

📁 الملفات المهمة فقط:

   run.py              - ابدأ هنا
   check.py            - فحص النظام
   simple_server.py    - خادم بسيط

   (تجاهل باقي الملفات)

═══════════════════════════════════════════════════════════════

💬 للمساعدة:

   أرسل لي:
   1. نتائج: python check.py
   2. أي رسائل خطأ تظهر
   3. نوع نظام التشغيل (Windows/Mac/Linux)

═══════════════════════════════════════════════════════════════
