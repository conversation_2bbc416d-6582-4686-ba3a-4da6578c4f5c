"""
مسارات التقارير والإحصائيات
"""
from fastapi import APIRouter, Depends, HTTPException, status, Response
from sqlalchemy.orm import Session
from sqlalchemy import func, extract
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, date
import io
from docx import Document
from docx.shared import Inches

from database import get_db
from models import AgeEstimationCase, Child, IntensityExamCase, GenderDeterminationCase, SexualAssaultCase, User
from routes.auth import get_current_user

router = APIRouter()

class ReportStats(BaseModel):
    total_cases: int
    monthly_cases: int
    yearly_cases: int
    gender_distribution: Dict[str, int]
    age_groups: Dict[str, int]
    authorities: Dict[str, int]

class UnifiedReportData(BaseModel):
    committee_date: date
    cases: List[Dict[str, Any]]

@router.get("/stats", response_model=ReportStats)
async def get_statistics(
    year: Optional[int] = None,
    month: Optional[int] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على الإحصائيات العامة"""
    current_year = year or datetime.now().year
    current_month = month or datetime.now().month
    
    # إجمالي الحالات
    total_cases = db.query(AgeEstimationCase).count()
    
    # الحالات الشهرية
    monthly_cases = db.query(AgeEstimationCase).filter(
        extract('year', AgeEstimationCase.committee_meeting_date) == current_year,
        extract('month', AgeEstimationCase.committee_meeting_date) == current_month
    ).count()
    
    # الحالات السنوية
    yearly_cases = db.query(AgeEstimationCase).filter(
        extract('year', AgeEstimationCase.committee_meeting_date) == current_year
    ).count()
    
    # توزيع الجنس
    gender_stats = db.query(
        Child.gender,
        func.count(Child.id)
    ).group_by(Child.gender).all()
    
    gender_distribution = {gender: count for gender, count in gender_stats}
    
    # الفئات العمرية
    age_groups = {
        "أقل من 5 سنوات": 0,
        "5-10 سنوات": 0,
        "10-15 سنة": 0,
        "15-18 سنة": 0,
        "أكثر من 18 سنة": 0
    }
    
    children = db.query(Child).filter(Child.age_numeric.isnot(None)).all()
    for child in children:
        age = child.age_numeric
        if age < 5:
            age_groups["أقل من 5 سنوات"] += 1
        elif age < 10:
            age_groups["5-10 سنوات"] += 1
        elif age < 15:
            age_groups["10-15 سنة"] += 1
        elif age < 18:
            age_groups["15-18 سنة"] += 1
        else:
            age_groups["أكثر من 18 سنة"] += 1
    
    # الجهات المرسلة
    authority_stats = db.query(
        AgeEstimationCase.sending_authority,
        func.count(AgeEstimationCase.id)
    ).group_by(AgeEstimationCase.sending_authority).all()
    
    authorities = {authority: count for authority, count in authority_stats}
    
    return ReportStats(
        total_cases=total_cases,
        monthly_cases=monthly_cases,
        yearly_cases=yearly_cases,
        gender_distribution=gender_distribution,
        age_groups=age_groups,
        authorities=authorities
    )

@router.get("/age-estimation/individual/{case_id}")
async def generate_individual_report(
    case_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """توليد تقرير مفرد لحالة تقدير أعمار"""
    case = db.query(AgeEstimationCase).filter(AgeEstimationCase.id == case_id).first()
    if not case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الحالة غير موجودة"
        )
    
    # إنشاء مستند Word
    doc = Document()
    
    # إضافة العنوان
    title = doc.add_heading('تقرير تقدير الأعمار', 0)
    title.alignment = 2  # محاذاة وسط
    
    # إضافة معلومات الحالة
    doc.add_paragraph(f'الرقم الفني: {case.technical_number}')
    doc.add_paragraph(f'الجهة المرسلة: {case.sending_authority}')
    doc.add_paragraph(f'اسم رب الأسرة: {case.family_head_name}')
    doc.add_paragraph(f'تاريخ اجتماع اللجنة: {case.committee_meeting_date.strftime("%Y-%m-%d")}')
    
    if case.court_letter_number:
        doc.add_paragraph(f'رقم كتاب المحكمة: {case.court_letter_number}')
    
    if case.court_letter_date:
        doc.add_paragraph(f'تاريخ كتاب المحكمة: {case.court_letter_date.strftime("%Y-%m-%d")}')
    
    # إضافة جدول الأطفال
    if case.children:
        doc.add_heading('بيانات الأطفال', level=1)
        table = doc.add_table(rows=1, cols=6)
        table.style = 'Table Grid'
        
        # رؤوس الجدول
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'التسلسل'
        hdr_cells[1].text = 'الاسم'
        hdr_cells[2].text = 'الجنس'
        hdr_cells[3].text = 'العمر رقماً'
        hdr_cells[4].text = 'العمر كتابة'
        hdr_cells[5].text = 'الملاحظات'
        
        # إضافة بيانات الأطفال
        for child in case.children:
            row_cells = table.add_row().cells
            row_cells[0].text = str(child.sequence)
            row_cells[1].text = child.name
            row_cells[2].text = child.gender
            row_cells[3].text = str(child.age_numeric) if child.age_numeric else ''
            row_cells[4].text = child.age_text or ''
            row_cells[5].text = child.notes or ''
    
    # إضافة الملاحظات
    if case.notes:
        doc.add_heading('الملاحظات', level=1)
        doc.add_paragraph(case.notes)
    
    # حفظ المستند في الذاكرة
    doc_io = io.BytesIO()
    doc.save(doc_io)
    doc_io.seek(0)
    
    # إرجاع الملف
    return Response(
        content=doc_io.getvalue(),
        media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        headers={"Content-Disposition": f"attachment; filename=تقرير_مفرد_{case.technical_number}.docx"}
    )

@router.get("/age-estimation/unified")
async def generate_unified_report(
    committee_date: date,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """توليد تقرير موحد لحالات تقدير الأعمار"""
    cases = db.query(AgeEstimationCase).filter(
        func.date(AgeEstimationCase.committee_meeting_date) == committee_date
    ).all()
    
    if not cases:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="لا توجد حالات في هذا التاريخ"
        )
    
    # إنشاء مستند Word
    doc = Document()
    
    # إضافة العنوان
    title = doc.add_heading('التقرير الموحد لتقدير الأعمار', 0)
    title.alignment = 2
    
    doc.add_paragraph(f'تاريخ اجتماع اللجنة: {committee_date.strftime("%Y-%m-%d")}')
    
    # إنشاء الجدول
    table = doc.add_table(rows=1, cols=6)
    table.style = 'Table Grid'
    
    # رؤوس الجدول
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'التسلسل'
    hdr_cells[1].text = 'اسم رب الأسرة'
    hdr_cells[2].text = 'عدد الأولاد'
    hdr_cells[3].text = 'المحكمة'
    hdr_cells[4].text = 'العدد'
    hdr_cells[5].text = 'التاريخ'
    
    # إضافة بيانات الحالات
    for i, case in enumerate(cases, 1):
        row_cells = table.add_row().cells
        row_cells[0].text = str(i)
        row_cells[1].text = case.family_head_name
        row_cells[2].text = str(len(case.children))
        row_cells[3].text = case.sending_authority
        row_cells[4].text = ''  # فارغ كما هو مطلوب
        row_cells[5].text = ''  # فارغ كما هو مطلوب
    
    # حفظ المستند
    doc_io = io.BytesIO()
    doc.save(doc_io)
    doc_io.seek(0)
    
    return Response(
        content=doc_io.getvalue(),
        media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        headers={"Content-Disposition": f"attachment; filename=تقرير_موحد_{committee_date}.docx"}
    )
