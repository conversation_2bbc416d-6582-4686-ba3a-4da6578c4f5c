#!/usr/bin/env python3
"""
تثبيت بسيط للمكتبات الأساسية فقط
"""
import subprocess
import sys

def install_package(package):
    """تثبيت مكتبة واحدة"""
    print(f"📦 تثبيت {package}...")
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', package
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print(f"✅ {package} - تم التثبيت")
            return True
        else:
            print(f"❌ {package} - فشل: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {package} - انتهت المهلة الزمنية")
        return False
    except Exception as e:
        print(f"❌ {package} - خطأ: {e}")
        return False

def main():
    print("📦 تثبيت المكتبات الأساسية لبرنامج شعبة الأحياء")
    print("=" * 55)
    
    # فحص pip
    print("🔧 فحص pip...")
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ pip متوفر: {result.stdout.strip()}")
        else:
            print("❌ pip غير متوفر")
            print("💡 جرب: python -m ensurepip --upgrade")
            return
    except Exception as e:
        print(f"❌ خطأ في pip: {e}")
        return
    
    # تحديث pip
    print("\n🔄 تحديث pip...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      capture_output=True, timeout=30)
        print("✅ تم تحديث pip")
    except:
        print("⚠️ لم يتم تحديث pip")
    
    # المكتبات الأساسية
    packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy', 
        'pydantic'
    ]
    
    print(f"\n📋 تثبيت {len(packages)} مكتبات أساسية...")
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 55)
    print(f"📊 النتائج: {success_count}/{len(packages)} مكتبات تم تثبيتها")
    
    if success_count >= 2:  # FastAPI + Uvicorn على الأقل
        print("✅ يمكن تشغيل خادم أساسي")
        print("🚀 جرب: python test_basic.py")
    else:
        print("❌ لم يتم تثبيت المكتبات الأساسية")
        print("💡 جرب تثبيت كل مكتبة منفصلة:")
        for package in packages:
            print(f"   pip install {package}")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
