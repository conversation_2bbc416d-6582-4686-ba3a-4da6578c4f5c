#!/usr/bin/env python3
"""
تشغيل خادم شعبة الأحياء
"""
import os
import sys

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("🚀 بدء تشغيل خادم شعبة الأحياء...")
    
    try:
        # استيراد الوحدات المطلوبة
        from database import init_db
        from main import app
        import uvicorn
        
        print("📦 تم تحميل الوحدات بنجاح")
        
        # إنشاء قاعدة البيانات
        print("🗄️ إعداد قاعدة البيانات...")
        init_db()
        
        # إنشاء المستخدمين الافتراضيين
        print("👤 إنشاء المستخدمين الافتراضيين...")
        try:
            from create_admin import create_admin_user
            create_admin_user()
        except Exception as e:
            print(f"⚠️ تحذير: {e}")
        
        print("🌐 بدء تشغيل الخادم...")
        print("📍 الخادم متاح على: http://localhost:8000")
        print("📚 وثائق API: http://localhost:8000/api/docs")
        print("🛑 اضغط Ctrl+C لإيقاف الخادم")
        
        # تشغيل الخادم
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            reload=False
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
