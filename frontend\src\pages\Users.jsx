import React, { useState } from 'react'
import { Card, Table, Button, Space, Typography, Tag, Alert } from 'antd'
import { UserOutlined, LockOutlined } from '@ant-design/icons'
import { useQuery } from 'react-query'
import dayjs from 'dayjs'
import { usersAPI } from '../services/api'
import { useAuth } from '../contexts/AuthContext'

const { Title } = Typography

const Users = () => {
  const { isAdmin } = useAuth()

  if (!isAdmin) {
    return (
      <Card className="neumorphic-card">
        <Alert
          message="وصول مقيد"
          description="ليس لديك صلاحية للوصول إلى هذا القسم. هذا القسم مخصص لمديري النظام فقط."
          type="warning"
          showIcon
          icon={<LockOutlined />}
        />
      </Card>
    )
  }

  const { data: users, isLoading } = useQuery(
    ['users'],
    () => usersAPI.getAll()
  )

  const columns = [
    {
      title: 'اسم المستخدم',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: 'الاسم الكامل',
      dataIndex: 'full_name',
      key: 'full_name',
    },
    {
      title: 'البريد الإلكتروني',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'الدور',
      dataIndex: 'role',
      key: 'role',
      render: (role) => {
        const colors = {
          admin: 'red',
          manager: 'orange',
          user: 'blue'
        }
        const labels = {
          admin: 'مدير النظام',
          manager: 'مدير',
          user: 'مستخدم'
        }
        return <Tag color={colors[role]}>{labels[role]}</Tag>
      },
    },
    {
      title: 'الحالة',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'نشط' : 'غير نشط'}
        </Tag>
      ),
    },
    {
      title: 'تاريخ الإنشاء',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: 'آخر تسجيل دخول',
      dataIndex: 'last_login',
      key: 'last_login',
      render: (date) => date ? dayjs(date).format('YYYY-MM-DD HH:mm') : 'لم يسجل دخول',
    },
  ]

  return (
    <div>
      <Card className="neumorphic-card" style={{ marginBottom: 16 }}>
        <Title level={3} style={{ margin: 0 }}>
          <UserOutlined style={{ marginLeft: 8 }} />
          إدارة المستخدمين
        </Title>
      </Card>

      <Card className="neumorphic-card">
        <Table
          columns={columns}
          dataSource={users?.data || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} من ${total} مستخدم`,
          }}
        />
      </Card>
    </div>
  )
}

export default Users
