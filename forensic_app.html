<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج شعبة الأحياء - قسم الطبابة العدلية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .title-section h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 5px;
        }

        .title-section p {
            color: #666;
            font-size: 14px;
        }

        .nav-menu {
            display: flex;
            gap: 20px;
        }

        .nav-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .nav-btn.active {
            background: #4caf50;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page {
            display: none;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .page.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 24px;
        }

        .card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .card p {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #e8f5e8;
            color: #4caf50;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .alert-success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #4caf50;
        }

        .alert-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1976d2;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            overflow-y: auto;
        }

        .modal-content {
            background: white;
            margin: 20px auto;
            padding: 0;
            border-radius: 20px;
            width: 95%;
            max-width: 1000px;
            min-width: 400px;
            min-height: 300px;
            position: relative;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            resize: both;
            overflow: hidden;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 20px 20px 0 0;
            cursor: move;
            user-select: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
        }

        .modal-body {
            padding: 20px;
            overflow-y: auto;
            max-height: calc(90vh - 120px);
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
            border-radius: 0 0 20px 20px;
            text-align: center;
        }

        .close {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .close:hover {
            background: rgba(255,255,255,0.3);
        }

        .resize-handle {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 20px;
            height: 20px;
            background: linear-gradient(-45deg, transparent 0%, transparent 40%, #ccc 40%, #ccc 60%, transparent 60%);
            cursor: se-resize;
            border-radius: 0 0 20px 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">ش.أ</div>
                <div class="title-section">
                    <h1>برنامج شعبة الأحياء</h1>
                    <p>قسم الطبابة العدلية - محافظة ميسان</p>
                </div>
            </div>
            <div class="nav-menu">
                <button class="nav-btn active" onclick="showPage('dashboard')">الرئيسية</button>
                <button class="nav-btn" onclick="showPage('age-estimation')">تقدير الأعمار</button>
                <button class="nav-btn" onclick="showPage('gender-determination')">تحديد الجنس</button>
                <button class="nav-btn" onclick="showPage('reports')">التقارير</button>
                <button class="nav-btn" onclick="showPage('settings')">الإعدادات</button>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Dashboard Page -->
        <div id="dashboard" class="page active">
            <h2>لوحة التحكم الرئيسية</h2>

            <div class="alert alert-success">
                <strong>مرحباً بكم!</strong> النظام يعمل بنجاح. هذه نسخة تجريبية تفاعلية من برنامج شعبة الأحياء.
            </div>

            <div class="dashboard-grid">
                <div class="card" onclick="showPage('age-estimation')">
                    <div class="card-icon">👶</div>
                    <h3>تقدير الأعمار</h3>
                    <p>إدارة حالات تقدير أعمار الأطفال والمراهقين باستخدام الطرق العلمية المعتمدة</p>
                </div>

                <div class="card" onclick="showPage('gender-determination')">
                    <div class="card-icon">⚧</div>
                    <h3>تحديد الجنس</h3>
                    <p>تحديد الجنس البيولوجي باستخدام الفحوصات الطبية والمختبرية المتخصصة</p>
                </div>

                <div class="card" onclick="showModal('severity-modal')">
                    <div class="card-icon">🔬</div>
                    <h3>فحص الشدة</h3>
                    <p>تقييم وتسجيل حالات فحص الشدة الطبية والإصابات الجسدية</p>
                </div>

                <div class="card" onclick="showPage('reports')">
                    <div class="card-icon">📊</div>
                    <h3>التقارير والإحصائيات</h3>
                    <p>توليد التقارير المفصلة والإحصائيات الشاملة لجميع الحالات</p>
                </div>

                <div class="card" onclick="showModal('users-modal')">
                    <div class="card-icon">👥</div>
                    <h3>إدارة المستخدمين</h3>
                    <p>إدارة حسابات المستخدمين والصلاحيات والأدوار المختلفة</p>
                </div>

                <div class="card" onclick="showModal('backup-modal')">
                    <div class="card-icon">💾</div>
                    <h3>النسخ الاحتياطي</h3>
                    <p>إنشاء واستعادة النسخ الاحتياطية لحماية البيانات المهمة</p>
                </div>
            </div>

            <div class="alert alert-info">
                <strong>إحصائيات سريعة:</strong><br>
                • إجمالي الحالات: 1,247<br>
                • حالات تقدير الأعمار: 523<br>
                • حالات تحديد الجنس: 341<br>
                • التقارير المنجزة: 892
            </div>
        </div>

        <!-- Age Estimation Page -->
        <div id="age-estimation" class="page">
            <h2>تقدير الأعمار</h2>

            <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                <button class="btn" onclick="showModal('add-age-case-modal')">إضافة حالة جديدة</button>
                <button class="btn btn-success" onclick="exportAgeData()">تصدير البيانات</button>
            </div>

            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الحالة</th>
                        <th>رب الأسرة</th>
                        <th>عدد الأطفال</th>
                        <th>الجهة المرسلة</th>
                        <th>التاريخ</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="age-cases-table">
                    <tr>
                        <td>AGE-001</td>
                        <td>محمد علي حسن</td>
                        <td>3 أطفال</td>
                        <td>محكمة الأحداث - ميسان</td>
                        <td>2024-01-15</td>
                        <td><span class="status-badge status-active">مكتمل</span></td>
                        <td>
                            <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="viewAgeCase('AGE-001')">عرض</button>
                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;" onclick="editAgeCase('AGE-001')">تحرير</button>
                        </td>
                    </tr>
                    <tr>
                        <td>AGE-002</td>
                        <td>أحمد صالح محمود</td>
                        <td>2 أطفال</td>
                        <td>مديرية الأحوال المدنية</td>
                        <td>2024-01-14</td>
                        <td><span class="status-badge status-pending">قيد المراجعة</span></td>
                        <td>
                            <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="viewAgeCase('AGE-002')">عرض</button>
                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;" onclick="editAgeCase('AGE-002')">تحرير</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Gender Determination Page -->
        <div id="gender-determination" class="page">
            <h2>تحديد الجنس</h2>

            <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                <button class="btn" onclick="showModal('add-gender-case-modal')">إضافة حالة جديدة</button>
                <button class="btn btn-success" onclick="exportGenderData()">تصدير البيانات</button>
            </div>

            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الحالة</th>
                        <th>اسم المريض</th>
                        <th>الجنس المحدد</th>
                        <th>طريقة التحديد</th>
                        <th>التاريخ</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="gender-cases-table">
                    <tr>
                        <td>GEN-001</td>
                        <td>مريض مجهول الهوية</td>
                        <td>ذكر</td>
                        <td>فحص الحمض النووي</td>
                        <td>2024-01-13</td>
                        <td><span class="status-badge status-active">مكتمل</span></td>
                        <td>
                            <button class="btn" style="padding: 5px 10px; font-size: 12px;">عرض</button>
                            <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;">تحرير</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Reports Page -->
        <div id="reports" class="page">
            <h2>التقارير والإحصائيات</h2>

            <div class="dashboard-grid">
                <div class="card" onclick="generateReport('age')">
                    <div class="card-icon">📈</div>
                    <h3>تقرير تقدير الأعمار</h3>
                    <p>تقرير شامل لجميع حالات تقدير الأعمار</p>
                </div>

                <div class="card" onclick="generateReport('gender')">
                    <div class="card-icon">📊</div>
                    <h3>تقرير تحديد الجنس</h3>
                    <p>تقرير مفصل لحالات تحديد الجنس</p>
                </div>

                <div class="card" onclick="generateReport('monthly')">
                    <div class="card-icon">📅</div>
                    <h3>التقرير الشهري</h3>
                    <p>تقرير إحصائي شهري شامل</p>
                </div>

                <div class="card" onclick="generateReport('annual')">
                    <div class="card-icon">📋</div>
                    <h3>التقرير السنوي</h3>
                    <p>تقرير سنوي مفصل لجميع الأنشطة</p>
                </div>
            </div>

            <div id="report-result" style="margin-top: 20px;"></div>
        </div>

        <!-- Settings Page -->
        <div id="settings" class="page">
            <h2>إعدادات النظام</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="background: #f8f9fa; padding: 20px; border-radius: 15px;">
                    <h3>إعدادات المستخدم</h3>
                    <div class="form-group">
                        <label>اسم المستخدم:</label>
                        <input type="text" value="admin" readonly>
                    </div>
                    <div class="form-group">
                        <label>البريد الإلكتروني:</label>
                        <input type="email" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>الصلاحية:</label>
                        <select disabled>
                            <option>مدير النظام</option>
                        </select>
                    </div>
                    <button class="btn">حفظ التغييرات</button>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 15px;">
                    <h3>إعدادات النظام</h3>
                    <div class="form-group">
                        <label>لغة النظام:</label>
                        <select>
                            <option selected>العربية</option>
                            <option>English</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>المنطقة الزمنية:</label>
                        <select>
                            <option selected>بغداد (GMT+3)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>تنسيق التاريخ:</label>
                        <select>
                            <option selected>DD/MM/YYYY</option>
                            <option>MM/DD/YYYY</option>
                        </select>
                    </div>
                    <button class="btn">حفظ الإعدادات</button>
                </div>
            </div>

            <div class="alert alert-info" style="margin-top: 20px;">
                <strong>ملاحظة:</strong> هذه نسخة تجريبية. في النسخة الكاملة، ستتمكن من حفظ جميع الإعدادات في قاعدة البيانات.
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Add Age Case Modal -->
    <div id="add-age-case-modal" class="modal">
        <div class="modal-content" id="age-modal-content">
            <div class="modal-header" id="age-modal-header">
                <h3>📋 إضافة حالة تقدير عمر جديدة</h3>
                <div style="display: flex; gap: 5px; align-items: center;">
                    <button class="close" onclick="minimizeModal('add-age-case-modal')" title="تصغير">−</button>
                    <button class="close" onclick="maximizeModal('add-age-case-modal')" title="تكبير">□</button>
                    <button class="close" onclick="closeModal('add-age-case-modal')" title="إغلاق">&times;</button>
                </div>
            </div>

            <div class="modal-body">

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div class="form-group">
                    <label>الرقم الفني:</label>
                    <input type="text" id="technical-number" placeholder="أدخل الرقم الفني">
                </div>

                <div class="form-group">
                    <label>الجهة المرسلة:</label>
                    <input type="text" id="sending-authority" placeholder="أدخل الجهة المرسلة">
                </div>

                <div class="form-group">
                    <label>رقم كتاب المحكمة:</label>
                    <input type="text" id="court-book-number" placeholder="رقم كتاب المحكمة">
                </div>

                <div class="form-group">
                    <label>تاريخ كتاب المحكمة:</label>
                    <input type="date" id="court-book-date">
                </div>

                <div class="form-group">
                    <label>اسم رب الأسرة:</label>
                    <input type="text" id="family-head-name" placeholder="أدخل اسم رب الأسرة">
                </div>

                <div class="form-group">
                    <label>تاريخ اجتماع اللجنة:</label>
                    <input type="date" id="committee-meeting-date">
                </div>
            </div>

            <h4>معلومات الأطفال:</h4>

            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 2fr; gap: 10px; align-items: end;">
                    <div class="form-group" style="margin-bottom: 0;">
                        <label>الاسم:</label>
                        <input type="text" id="child-name" placeholder="اسم الطفل">
                    </div>

                    <div class="form-group" style="margin-bottom: 0;">
                        <label>الجنس:</label>
                        <select id="child-gender">
                            <option value="">اختر الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>

                    <div class="form-group" style="margin-bottom: 0;">
                        <label>العمر رقماً:</label>
                        <input type="text" id="child-age-number" placeholder="مثال: 15">
                    </div>

                    <div class="form-group" style="margin-bottom: 0;">
                        <label>العمر كتابة:</label>
                        <input type="text" id="child-age-text" placeholder="مثال: خمسة عشر سنة">
                    </div>

                    <div class="form-group" style="margin-bottom: 0;">
                        <label>الملاحظات:</label>
                        <input type="text" id="child-notes" placeholder="ملاحظات">
                    </div>

                    <div style="display: flex; gap: 5px;">
                        <button class="btn" onclick="addChild()" style="padding: 8px 12px; font-size: 12px;">إضافة طفل</button>
                        <button class="btn btn-success" onclick="updateChild()" style="padding: 8px 12px; font-size: 12px; display: none;" id="update-child-btn">تحديث</button>
                        <button class="btn btn-danger" onclick="cancelEdit()" style="padding: 8px 12px; font-size: 12px; display: none;" id="cancel-edit-btn">إلغاء</button>
                    </div>
                </div>
            </div>

            <div style="max-height: 300px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 10px;">
                <table class="table" style="margin: 0;">
                    <thead style="position: sticky; top: 0; background: white;">
                        <tr>
                            <th>تسلسل</th>
                            <th>الاسم</th>
                            <th>الجنس</th>
                            <th>العمر رقماً</th>
                            <th>العمر كتابة</th>
                            <th>الملاحظات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="children-table">
                        <!-- سيتم إضافة الأطفال هنا -->
                    </tbody>
                </table>
            </div>

            </div>

            <div class="modal-footer">
                <button class="btn btn-success" onclick="saveAgeCase()">حفظ الحالة</button>
                <button class="btn" onclick="exportToWord()" style="background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);">📄 تصدير إلى Word</button>
                <button class="btn btn-danger" onclick="closeModal('add-age-case-modal')">إلغاء</button>
            </div>

            <div class="resize-handle"></div>
        </div>
    </div>

    <!-- Add Gender Case Modal -->
    <div id="add-gender-case-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('add-gender-case-modal')">&times;</span>
            <h3>إضافة حالة تحديد جنس جديدة</h3>

            <div class="form-group">
                <label>اسم المريض:</label>
                <input type="text" id="gender-patient-name" placeholder="أدخل اسم المريض">
            </div>

            <div class="form-group">
                <label>طريقة التحديد:</label>
                <select id="gender-method">
                    <option>فحص الحمض النووي</option>
                    <option>الفحص الهرموني</option>
                    <option>الفحص التشريحي</option>
                </select>
            </div>

            <div class="form-group">
                <label>ملاحظات:</label>
                <textarea id="gender-notes" rows="3" placeholder="أدخل أي ملاحظات إضافية"></textarea>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-success" onclick="addGenderCase()">إضافة الحالة</button>
                <button class="btn btn-danger" onclick="closeModal('add-gender-case-modal')">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- Other Modals -->
    <div id="severity-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('severity-modal')">&times;</span>
            <h3>فحص الشدة</h3>
            <p>هذه الميزة متاحة في النسخة الكاملة من البرنامج.</p>
            <div class="alert alert-info">
                يتضمن فحص الشدة تقييم الإصابات الجسدية وتحديد درجة خطورتها وتوثيق النتائج بشكل علمي دقيق.
            </div>
            <button class="btn" onclick="closeModal('severity-modal')">إغلاق</button>
        </div>
    </div>

    <div id="users-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('users-modal')">&times;</span>
            <h3>إدارة المستخدمين</h3>
            <p>المستخدمون المسجلون في النظام:</p>
            <table class="table">
                <tr><th>المستخدم</th><th>الصلاحية</th><th>الحالة</th></tr>
                <tr><td>admin</td><td>مدير النظام</td><td><span class="status-badge status-active">نشط</span></td></tr>
                <tr><td>user</td><td>مستخدم عادي</td><td><span class="status-badge status-active">نشط</span></td></tr>
            </table>
            <button class="btn" onclick="closeModal('users-modal')">إغلاق</button>
        </div>
    </div>

    <div id="backup-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('backup-modal')">&times;</span>
            <h3>النسخ الاحتياطي</h3>
            <div class="alert alert-info">
                <strong>آخر نسخة احتياطية:</strong> 2024-01-15 10:30 ص<br>
                <strong>حجم البيانات:</strong> 2.5 MB<br>
                <strong>عدد السجلات:</strong> 1,247 سجل
            </div>
            <div style="text-align: center;">
                <button class="btn btn-success">إنشاء نسخة احتياطية</button>
                <button class="btn">استعادة النسخة الاحتياطية</button>
            </div>
            <button class="btn btn-danger" onclick="closeModal('backup-modal')" style="margin-top: 15px;">إغلاق</button>
        </div>
    </div>

    <script>
        // Global variables
        let currentPage = 'dashboard';
        let ageCases = [];
        let genderCases = [];
        let caseCounter = 3;
        let currentChildren = [];
        let editingChildIndex = -1;

        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // Remove active class from all nav buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected page
            document.getElementById(pageId).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');

            currentPage = pageId;
        }

        // Modal functions
        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            const modalContent = modal.querySelector('.modal-content');

            // Reset position and size
            modalContent.style.transform = 'translate(0px, 0px)';
            modalContent.style.width = '';
            modalContent.style.height = '';

            modal.style.display = 'block';

            // Center the modal
            setTimeout(() => {
                const rect = modalContent.getBoundingClientRect();
                const centerX = (window.innerWidth - rect.width) / 2;
                const centerY = (window.innerHeight - rect.height) / 2;
                modalContent.style.transform = `translate(${centerX - rect.left}px, ${centerY - rect.top}px)`;
            }, 10);
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            clearModalForms();
        }

        function minimizeModal(modalId) {
            const modal = document.getElementById(modalId);
            const modalContent = modal.querySelector('.modal-content');

            if (modalContent.style.height === '50px') {
                // Restore
                modalContent.style.height = '';
                modalContent.querySelector('.modal-body').style.display = 'block';
                modalContent.querySelector('.modal-footer').style.display = 'block';
            } else {
                // Minimize
                modalContent.style.height = '50px';
                modalContent.querySelector('.modal-body').style.display = 'none';
                modalContent.querySelector('.modal-footer').style.display = 'none';
            }
        }

        function maximizeModal(modalId) {
            const modal = document.getElementById(modalId);
            const modalContent = modal.querySelector('.modal-content');

            if (modalContent.style.width === '95vw') {
                // Restore
                modalContent.style.width = '';
                modalContent.style.height = '';
                modalContent.style.transform = 'translate(0px, 0px)';
            } else {
                // Maximize
                modalContent.style.width = '95vw';
                modalContent.style.height = '90vh';
                modalContent.style.transform = 'translate(0px, 0px)';
            }
        }

        function clearModalForms() {
            document.querySelectorAll('input, textarea, select').forEach(input => {
                if (input.type !== 'button' && input.type !== 'submit') {
                    if (input.tagName === 'SELECT') {
                        input.selectedIndex = 0;
                    } else {
                        input.value = '';
                    }
                }
            });

            // مسح جدول الأطفال
            currentChildren = [];
            editingChildIndex = -1;
            updateChildrenTable();
            resetChildForm();
        }

        // إضافة طفل جديد
        function addChild() {
            const name = document.getElementById('child-name').value;
            const gender = document.getElementById('child-gender').value;
            const ageNumber = document.getElementById('child-age-number').value;
            const ageText = document.getElementById('child-age-text').value;
            const notes = document.getElementById('child-notes').value;

            if (!name || !gender) {
                alert('يرجى إدخال اسم الطفل والجنس على الأقل');
                return;
            }

            const child = {
                name: name,
                gender: gender,
                ageNumber: ageNumber,
                ageText: ageText,
                notes: notes
            };

            currentChildren.push(child);
            updateChildrenTable();
            resetChildForm();
        }

        // تحديث جدول الأطفال
        function updateChildrenTable() {
            const tableBody = document.getElementById('children-table');
            tableBody.innerHTML = '';

            currentChildren.forEach((child, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${child.name}</td>
                    <td>${child.gender}</td>
                    <td>${child.ageNumber}</td>
                    <td>${child.ageText}</td>
                    <td>${child.notes}</td>
                    <td>
                        <button class="btn" onclick="editChild(${index})" style="padding: 3px 8px; font-size: 11px;">تعديل</button>
                        <button class="btn btn-danger" onclick="deleteChild(${index})" style="padding: 3px 8px; font-size: 11px;">حذف</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // تعديل طفل
        function editChild(index) {
            const child = currentChildren[index];

            document.getElementById('child-name').value = child.name;
            document.getElementById('child-gender').value = child.gender;
            document.getElementById('child-age-number').value = child.ageNumber;
            document.getElementById('child-age-text').value = child.ageText;
            document.getElementById('child-notes').value = child.notes;

            editingChildIndex = index;

            // إخفاء زر الإضافة وإظهار أزرار التحديث والإلغاء
            document.querySelector('button[onclick="addChild()"]').style.display = 'none';
            document.getElementById('update-child-btn').style.display = 'inline-block';
            document.getElementById('cancel-edit-btn').style.display = 'inline-block';
        }

        // تحديث طفل
        function updateChild() {
            if (editingChildIndex === -1) return;

            const name = document.getElementById('child-name').value;
            const gender = document.getElementById('child-gender').value;
            const ageNumber = document.getElementById('child-age-number').value;
            const ageText = document.getElementById('child-age-text').value;
            const notes = document.getElementById('child-notes').value;

            if (!name || !gender) {
                alert('يرجى إدخال اسم الطفل والجنس على الأقل');
                return;
            }

            currentChildren[editingChildIndex] = {
                name: name,
                gender: gender,
                ageNumber: ageNumber,
                ageText: ageText,
                notes: notes
            };

            updateChildrenTable();
            resetChildForm();
        }

        // حذف طفل
        function deleteChild(index) {
            if (confirm('هل أنت متأكد من حذف هذا الطفل؟')) {
                currentChildren.splice(index, 1);
                updateChildrenTable();
            }
        }

        // إلغاء التعديل
        function cancelEdit() {
            resetChildForm();
        }

        // إعادة تعيين نموذج الطفل
        function resetChildForm() {
            document.getElementById('child-name').value = '';
            document.getElementById('child-gender').value = '';
            document.getElementById('child-age-number').value = '';
            document.getElementById('child-age-text').value = '';
            document.getElementById('child-notes').value = '';

            editingChildIndex = -1;

            // إظهار زر الإضافة وإخفاء أزرار التحديث والإلغاء
            document.querySelector('button[onclick="addChild()"]').style.display = 'inline-block';
            document.getElementById('update-child-btn').style.display = 'none';
            document.getElementById('cancel-edit-btn').style.display = 'none';
        }

        // حفظ حالة تقدير الأعمار الجديدة
        function saveAgeCase() {
            const technicalNumber = document.getElementById('technical-number').value;
            const sendingAuthority = document.getElementById('sending-authority').value;
            const courtBookNumber = document.getElementById('court-book-number').value;
            const courtBookDate = document.getElementById('court-book-date').value;
            const familyHeadName = document.getElementById('family-head-name').value;
            const committeeMeetingDate = document.getElementById('committee-meeting-date').value;

            if (!technicalNumber || !sendingAuthority || !familyHeadName || currentChildren.length === 0) {
                alert('يرجى ملء الحقول الأساسية وإضافة طفل واحد على الأقل');
                return;
            }

            const newCase = {
                id: `AGE-${String(caseCounter).padStart(3, '0')}`,
                technicalNumber: technicalNumber,
                sendingAuthority: sendingAuthority,
                courtBookNumber: courtBookNumber,
                courtBookDate: courtBookDate,
                familyHeadName: familyHeadName,
                committeeMeetingDate: committeeMeetingDate,
                children: [...currentChildren],
                date: new Date().toLocaleDateString('ar-SA'),
                status: 'قيد المراجعة'
            };

            ageCases.push(newCase);
            caseCounter++;

            updateAgeCasesTable();
            closeModal('add-age-case-modal');

            alert('تم حفظ الحالة بنجاح!');
        }

        // Add new gender determination case
        function addGenderCase() {
            const patientName = document.getElementById('gender-patient-name').value;
            const method = document.getElementById('gender-method').value;
            const notes = document.getElementById('gender-notes').value;

            if (!patientName) {
                alert('يرجى إدخال اسم المريض');
                return;
            }

            const newCase = {
                id: `GEN-${String(caseCounter).padStart(3, '0')}`,
                patientName: patientName,
                gender: 'قيد التحديد',
                method: method,
                date: new Date().toLocaleDateString('ar-SA'),
                status: 'قيد المراجعة',
                notes: notes
            };

            genderCases.push(newCase);
            caseCounter++;

            updateGenderCasesTable();
            closeModal('add-gender-case-modal');

            alert('تم إضافة الحالة بنجاح!');
        }

        // Update age cases table
        function updateAgeCasesTable() {
            const tableBody = document.getElementById('age-cases-table');

            // مسح الصفوف الجديدة فقط (الاحتفاظ بالصفوف الأصلية)
            const newRows = tableBody.querySelectorAll('tr[data-new="true"]');
            newRows.forEach(row => row.remove());

            ageCases.forEach(caseData => {
                const row = document.createElement('tr');
                row.setAttribute('data-new', 'true');
                row.innerHTML = `
                    <td>${caseData.id}</td>
                    <td>${caseData.familyHeadName}</td>
                    <td>${caseData.children.length} طفل/أطفال</td>
                    <td>${caseData.sendingAuthority}</td>
                    <td>${caseData.date}</td>
                    <td><span class="status-badge status-pending">${caseData.status}</span></td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="viewAgeCase('${caseData.id}')">عرض</button>
                        <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;" onclick="editAgeCase('${caseData.id}')">تحرير</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Update gender cases table
        function updateGenderCasesTable() {
            const tableBody = document.getElementById('gender-cases-table');

            genderCases.forEach(caseData => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${caseData.id}</td>
                    <td>${caseData.patientName}</td>
                    <td>${caseData.gender}</td>
                    <td>${caseData.method}</td>
                    <td>${caseData.date}</td>
                    <td><span class="status-badge status-pending">${caseData.status}</span></td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px;" onclick="viewCase('${caseData.id}')">عرض</button>
                        <button class="btn btn-success" style="padding: 5px 10px; font-size: 12px;" onclick="editCase('${caseData.id}')">تحرير</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        // عرض تفاصيل حالة تقدير الأعمار
        function viewAgeCase(caseId) {
            const caseData = ageCases.find(c => c.id === caseId);

            if (!caseData) {
                alert(`عرض تفاصيل الحالة: ${caseId}\n\nهذه حالة نموذجية. في النسخة الكاملة ستظهر جميع التفاصيل.`);
                return;
            }

            let childrenDetails = '';
            caseData.children.forEach((child, index) => {
                childrenDetails += `${index + 1}. ${child.name} - ${child.gender} - العمر: ${child.ageNumber} (${child.ageText})\n`;
            });

            alert(`تفاصيل الحالة: ${caseData.id}\n\n` +
                  `الرقم الفني: ${caseData.technicalNumber}\n` +
                  `الجهة المرسلة: ${caseData.sendingAuthority}\n` +
                  `رب الأسرة: ${caseData.familyHeadName}\n` +
                  `عدد الأطفال: ${caseData.children.length}\n\n` +
                  `تفاصيل الأطفال:\n${childrenDetails}`);
        }

        // تحرير حالة تقدير الأعمار
        function editAgeCase(caseId) {
            alert(`تحرير الحالة: ${caseId}\n\nهذه الميزة متاحة في النسخة الكاملة من البرنامج.`);
        }

        // View case details (للحالات الأخرى)
        function viewCase(caseId) {
            alert(`عرض تفاصيل الحالة: ${caseId}\n\nهذه الميزة متاحة في النسخة الكاملة من البرنامج.`);
        }

        // Edit case (للحالات الأخرى)
        function editCase(caseId) {
            alert(`تحرير الحالة: ${caseId}\n\nهذه الميزة متاحة في النسخة الكاملة من البرنامج.`);
        }

        // Export functions
        function exportAgeData() {
            alert('تصدير بيانات تقدير الأعمار...\n\nسيتم تصدير البيانات إلى ملف Excel في النسخة الكاملة.');
        }

        function exportGenderData() {
            alert('تصدير بيانات تحديد الجنس...\n\nسيتم تصدير البيانات إلى ملف Excel في النسخة الكاملة.');
        }

        // Generate reports
        function generateReport(reportType) {
            const reportResult = document.getElementById('report-result');
            let reportContent = '';

            switch(reportType) {
                case 'age':
                    reportContent = `
                        <div class="alert alert-success">
                            <h4>تقرير تقدير الأعمار</h4>
                            <p><strong>الفترة:</strong> يناير 2024</p>
                            <p><strong>إجمالي الحالات:</strong> 523 حالة</p>
                            <p><strong>الحالات المكتملة:</strong> 487 حالة (93%)</p>
                            <p><strong>الحالات قيد المراجعة:</strong> 36 حالة (7%)</p>
                            <p><strong>متوسط العمر المقدر:</strong> 15.2 سنة</p>
                        </div>
                    `;
                    break;
                case 'gender':
                    reportContent = `
                        <div class="alert alert-success">
                            <h4>تقرير تحديد الجنس</h4>
                            <p><strong>الفترة:</strong> يناير 2024</p>
                            <p><strong>إجمالي الحالات:</strong> 341 حالة</p>
                            <p><strong>ذكور:</strong> 198 حالة (58%)</p>
                            <p><strong>إناث:</strong> 143 حالة (42%)</p>
                            <p><strong>دقة التحديد:</strong> 99.7%</p>
                        </div>
                    `;
                    break;
                case 'monthly':
                    reportContent = `
                        <div class="alert alert-success">
                            <h4>التقرير الشهري - يناير 2024</h4>
                            <p><strong>إجمالي الحالات الجديدة:</strong> 127 حالة</p>
                            <p><strong>الحالات المكتملة:</strong> 98 حالة</p>
                            <p><strong>معدل الإنجاز:</strong> 77%</p>
                            <p><strong>أكثر الفحوصات طلباً:</strong> تقدير الأعمار (65%)</p>
                        </div>
                    `;
                    break;
                case 'annual':
                    reportContent = `
                        <div class="alert alert-success">
                            <h4>التقرير السنوي - 2024</h4>
                            <p><strong>إجمالي الحالات:</strong> 1,247 حالة</p>
                            <p><strong>تقدير الأعمار:</strong> 523 حالة (42%)</p>
                            <p><strong>تحديد الجنس:</strong> 341 حالة (27%)</p>
                            <p><strong>فحص الشدة:</strong> 383 حالة (31%)</p>
                            <p><strong>معدل الدقة الإجمالي:</strong> 98.5%</p>
                        </div>
                    `;
                    break;
            }

            reportResult.innerHTML = reportContent;
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // Make modal draggable
        function makeDraggable(modal) {
            const modalContent = modal.querySelector('.modal-content');
            const header = modal.querySelector('.modal-header');
            let isDragging = false;
            let currentX;
            let currentY;
            let initialX;
            let initialY;
            let xOffset = 0;
            let yOffset = 0;

            header.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            function dragStart(e) {
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;

                if (e.target === header || header.contains(e.target)) {
                    isDragging = true;
                    modalContent.style.cursor = 'grabbing';
                }
            }

            function drag(e) {
                if (isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;

                    xOffset = currentX;
                    yOffset = currentY;

                    modalContent.style.transform = `translate(${currentX}px, ${currentY}px)`;
                }
            }

            function dragEnd(e) {
                initialX = currentX;
                initialY = currentY;
                isDragging = false;
                modalContent.style.cursor = 'default';
            }
        }

        // Make modal resizable
        function makeResizable(modal) {
            const modalContent = modal.querySelector('.modal-content');
            const resizeHandle = modal.querySelector('.resize-handle');
            let isResizing = false;

            if (resizeHandle) {
                resizeHandle.addEventListener('mousedown', initResize);
            }

            function initResize(e) {
                isResizing = true;
                document.addEventListener('mousemove', doResize);
                document.addEventListener('mouseup', stopResize);
            }

            function doResize(e) {
                if (!isResizing) return;

                const rect = modalContent.getBoundingClientRect();
                const newWidth = e.clientX - rect.left;
                const newHeight = e.clientY - rect.top;

                if (newWidth > 300) {
                    modalContent.style.width = newWidth + 'px';
                }
                if (newHeight > 200) {
                    modalContent.style.height = newHeight + 'px';
                }
            }

            function stopResize() {
                isResizing = false;
                document.removeEventListener('mousemove', doResize);
                document.removeEventListener('mouseup', stopResize);
            }
        }

        // دالة تصدير إلى Word
        function exportToWord() {
            try {
                const technicalNumber = document.getElementById('technical-number').value || 'غير محدد';
                const sendingAuthority = document.getElementById('sending-authority').value || 'غير محدد';
                const courtBookNumber = document.getElementById('court-book-number').value || 'غير محدد';
                const courtBookDate = document.getElementById('court-book-date').value || 'غير محدد';
                const familyHeadName = document.getElementById('family-head-name').value || 'غير محدد';
                const committeeMeetingDate = document.getElementById('committee-meeting-date').value || 'غير محدد';

                if (currentChildren.length === 0) {
                    alert('يرجى إضافة طفل واحد على الأقل قبل التصدير');
                    return;
                }

                const wordContent = generateWordDocument(
                    technicalNumber,
                    sendingAuthority,
                    courtBookNumber,
                    courtBookDate,
                    familyHeadName,
                    committeeMeetingDate,
                    currentChildren
                );

                const blob = new Blob([wordContent], {
                    type: 'application/msword'
                });

                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                const childrenNames = currentChildren.map(child => child.name).join('_');
                const currentDate = new Date().toLocaleDateString('en-GB').replace(/\//g, '-');
                const fileName = `تقرير_تقدير_الأعمار_${familyHeadName}_${childrenNames}_${currentDate}.doc`;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                alert('تم تصدير التقرير بنجاح!');
            } catch (error) {
                console.error('خطأ في التصدير:', error);
                alert('حدث خطأ أثناء التصدير. يرجى المحاولة مرة أخرى.');
            }
        }

        function generateWordDocument(technicalNumber, sendingAuthority, courtBookNumber, courtBookDate, familyHeadName, committeeMeetingDate, children) {
            const formatDate = (dateStr) => {
                if (!dateStr || dateStr === 'غير محدد') return 'غير محدد';
                // إذا كان التاريخ بالتنسيق المطلوب بالفعل، أرجعه كما هو
                if (dateStr.match(/^\d{2}-\d{2}-\d{4}$/)) {
                    return dateStr;
                }
                // إذا كان بتنسيق HTML date input (YYYY-MM-DD)، حوله إلى DD-MM-YYYY
                if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    const [year, month, day] = dateStr.split('-');
                    return `${day}-${month}-${year}`;
                }
                // للتنسيقات الأخرى، استخدم Date object
                const date = new Date(dateStr);
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                return `${day}-${month}-${year}`;
            };

            let childrenTable = '';
            children.forEach((child, index) => {
                childrenTable += `
                    <tr>
                        <td style="border: 1px solid black; padding: 3px 5px; text-align: center; font-weight: bold; font-size: 11pt; height: 16px; vertical-align: middle; line-height: 1.1;">${index + 1}</td>
                        <td style="border: 1px solid black; padding: 3px 5px; text-align: center; font-size: 11pt; height: 16px; vertical-align: middle; line-height: 1.1;">${child.name}</td>
                        <td style="border: 1px solid black; padding: 3px 5px; text-align: center; font-size: 11pt; height: 16px; vertical-align: middle; line-height: 1.1;">${child.gender}</td>
                        <td style="border: 1px solid black; padding: 3px 5px; text-align: center; font-size: 11pt; height: 16px; vertical-align: middle; line-height: 1.1;">${child.ageNumber}</td>
                        <td style="border: 1px solid black; padding: 3px 5px; text-align: center; font-size: 11pt; height: 16px; vertical-align: middle; line-height: 1.1;">${child.ageText}</td>
                        <td style="border: 1px solid black; padding: 3px 5px; text-align: center; font-size: 11pt; height: 16px; vertical-align: middle; line-height: 1.1;">${child.notes || ''}</td>
                    </tr>
                `;
            });

            const content = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1;
            margin: 0;
            padding: 15px;
            direction: rtl;
            text-align: center;
            color: black;
        }
        .technical-number {
            position: absolute;
            top: 15px;
            left: 15px;
            font-weight: bold;
            font-size: 14pt;
            direction: ltr;
            text-align: left;
        }
        .header-section {
            text-align: center;
            margin-top: 25px;
            margin-bottom: 15px;
            line-height: 1;
        }
        .reference-line {
            margin: 5px 0;
            font-size: 12pt;
            line-height: 1;
            text-align: center;
        }
        .director-line {
            margin: 8px 0;
            font-size: 14pt;
            text-align: left;
            direction: ltr;
        }
        .report-title {
            text-align: center;
            font-size: 16pt;
            font-weight: bold;
            margin: 10px 0 8px 0;
            text-decoration: underline;
        }
        .report-content {
            margin: 5px 0;
            font-size: 12pt;
            line-height: 1;
            text-align: center;
        }
        .children-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            border: 2px solid black;
            font-size: 12pt;
            table-layout: fixed;
        }
        .children-table th {
            border: 1px solid black;
            padding: 3px 5px;
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
            font-size: 11pt;
            word-wrap: break-word;
            line-height: 1.1;
            height: 18px;
            vertical-align: middle;
        }
        .children-table td {
            border: 1px solid black;
            padding: 3px 5px;
            text-align: center;
            font-size: 11pt;
            word-wrap: break-word;
            overflow-wrap: break-word;
            line-height: 1.1;
            height: 16px;
            vertical-align: middle;
        }
        .end-note {
            text-align: center;
            font-weight: bold;
            font-size: 14pt;
            margin: 15px 0;
        }
        .committee-title {
            text-align: center;
            font-weight: bold;
            font-size: 16pt;
            margin: 20px 0 2px 0;
        }
        .signatures {
            margin-top: 60px;
            text-align: center;
            direction: ltr;
            width: 100%;
            font-size: 12pt;
            font-weight: bold;
        }
        .signature-row {
            display: inline-block;
            width: 100%;
            white-space: nowrap;
        }
        .signature-item {
            display: inline-block;
            width: 30%;
            font-size: 12pt;
            font-weight: bold;
            text-align: center;
            vertical-align: top;
            margin: 0 1.5%;
        }
        .signature-line {
            border-bottom: 1px solid black;
            margin-top: 40px;
            height: 1px;
            width: 120px;
            margin-left: auto;
            margin-right: auto;
        }
    </style>
</head>
<body>
    <div class="technical-number">
        الرقم الفني: ${technicalNumber}
    </div>

    <div class="header-section">
        <div style="margin: 10px 0; font-size: 14pt;">إلى/ ${sendingAuthority}</div>
        <div style="margin: 10px 0; font-size: 14pt;">م/ تقدير عمر</div>
        <div style="margin: 10px 0; font-size: 14pt;">******************</div>
    </div>

    <div class="reference-line">
        إشارة الى كتابكم المرقم (${courtBookNumber}) في ${formatDate(courtBookDate)} أدناه التقرير الطبي الخاص بتقدير عمر أولاد المدعو (${familyHeadName})
    </div>

    <div class="director-line">
        <div>مدير قسم الطب العدلي</div>
        <div style="margin-top: 10px;">${new Date().getFullYear()}</div>
    </div>

    <div class="report-title">
        التقرير الطبي
    </div>

    <div class="report-content">
        اجتمعت اللجنة الطبية في يوم الثلاثاء الموافق ${formatDate(committeeMeetingDate)}
    </div>

    <div class="report-content">
        وبالاعتماد على الفحوصات الشعاعية والسريرية والفحص الظاهري كونت عمر المدعو هو:
    </div>

    <table class="children-table">
        <thead>
            <tr>
                <th style="width: 6%;">ت</th>
                <th style="width: 22%;">الاسم</th>
                <th style="width: 10%;">الجنس</th>
                <th style="width: 12%;">العمر رقماً</th>
                <th style="width: 28%;">العمر كتابياً</th>
                <th style="width: 22%;">الملاحظات</th>
            </tr>
        </thead>
        <tbody>
            ${childrenTable}
        </tbody>
    </table>

    <div class="end-note">
        (انتهى)
    </div>

    <div class="committee-title">
        اللجنة
    </div>

    <div class="signatures">
        <table style="width: 100%; border: none; margin-top: 5px; table-layout: auto;">
            <tr>
                <td style="text-align: center; border: none; font-size: 12pt; font-weight: bold; padding: 0 20px;">
                    رئيس اللجنة
                    <div style="border-bottom: 1px solid black; margin-top: 15px; width: 100px; margin-left: auto; margin-right: auto;"></div>
                </td>
                <td style="text-align: center; border: none; font-size: 12pt; font-weight: bold; padding: 0 20px;">
                    عضو
                    <div style="border-bottom: 1px solid black; margin-top: 15px; width: 80px; margin-left: auto; margin-right: auto;"></div>
                </td>
                <td style="text-align: center; border: none; font-size: 12pt; font-weight: bold; padding: 0 20px;">
                    عضو
                    <div style="border-bottom: 1px solid black; margin-top: 15px; width: 80px; margin-left: auto; margin-right: auto;"></div>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
            `;

            return content;
        }
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Make modals draggable and resizable
            const ageModal = document.getElementById('add-age-case-modal');
            if (ageModal) {
                makeDraggable(ageModal);
                makeResizable(ageModal);
            }

            // Show welcome message
            setTimeout(() => {
                alert('مرحباً بكم في النسخة المحدثة من برنامج شعبة الأحياء!\n\n✅ التحديثات الجديدة:\n• العنوان: محافظة ميسان\n• نموذج تقدير أعمار محسن\n• نوافذ قابلة للسحب والتحريك\n• إدارة متقدمة للأطفال\n• تصدير التقارير إلى Word\n\n🎯 يمكنكم الآن سحب النوافذ وتصدير التقارير!');
            }, 1000);
        });
    </script>
</body>
</html>