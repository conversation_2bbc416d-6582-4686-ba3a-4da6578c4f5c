#!/usr/bin/env python3
"""
System check for Forensic Biology Program
"""
import sys
import os

def main():
    print("=" * 60)
    print("FORENSIC BIOLOGY PROGRAM - SYSTEM CHECK")
    print("=" * 60)
    
    # Python version
    version = sys.version_info
    print(f"Python Version: {version.major}.{version.minor}.{version.micro}")
    print(f"Python Path: {sys.executable}")
    print(f"Current Directory: {os.getcwd()}")
    
    if version < (3, 6):
        print("WARNING: Python 3.6+ recommended")
    else:
        print("SUCCESS: Python version is compatible")
    
    # Check files
    print("\nChecking files:")
    required_files = [
        'simple_run.py',
        'start.bat'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"  FOUND: {file}")
        else:
            print(f"  MISSING: {file}")
    
    # Check imports
    print("\nChecking libraries:")
    
    basic_libs = [
        ('json', 'JSON support'),
        ('http.server', 'HTTP server'),
        ('webbrowser', 'Browser support'),
        ('threading', 'Threading support')
    ]
    
    for lib, desc in basic_libs:
        try:
            __import__(lib)
            print(f"  SUCCESS: {desc}")
        except ImportError:
            print(f"  ERROR: {desc} - not available")
    
    # Check optional libraries
    print("\nChecking optional libraries:")
    
    optional_libs = [
        ('fastapi', 'FastAPI framework'),
        ('uvicorn', 'ASGI server'),
        ('sqlalchemy', 'Database ORM')
    ]
    
    for lib, desc in optional_libs:
        try:
            __import__(lib)
            print(f"  AVAILABLE: {desc}")
        except ImportError:
            print(f"  NOT INSTALLED: {desc}")
    
    print("\n" + "=" * 60)
    print("RECOMMENDATIONS:")
    print("1. To run the program: python simple_run.py")
    print("2. Or double-click: start.bat")
    print("3. Then open: http://localhost:8000")
    print("=" * 60)
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
