═══════════════════════════════════════════════════════════════
                برنامج شعبة الأحياء - قسم الطبابة العدلية
                         تعليمات التشغيل
═══════════════════════════════════════════════════════════════

🚀 الطريقة الأسهل (جرب هذه أولاً):

   Windows:
   ========
   1. اضغط مرتين على: تشغيل.bat
   
   أو
   
   2. اضغط مرتين على: تشغيل_مباشر.py

───────────────────────────────────────────────────────────────

🔧 إذا لم تعمل الطريقة الأولى:

   1. افتح Command Prompt
   
   2. جرب هذه الأوامر واحداً تلو الآخر:
      py تشغيل_مباشر.py
      python تشغيل_مباشر.py
      python3 تشغيل_مباشر.py

───────────────────────────────────────────────────────────────

❌ إذا ظهر خطأ "python غير موجود":

   1. تحميل Python من: https://python.org/downloads/
   
   2. أثناء التثبيت:
      ✅ اختر "Add Python to PATH"
      ✅ اختر "Install for all users"
   
   3. إعادة تشغيل الكمبيوتر
   
   4. فتح Command Prompt جديد
   
   5. اختبار: py --version

───────────────────────────────────────────────────────────────

✅ إذا عمل البرنامج:

   - سيفتح المتصفح تلقائياً
   - أو افتح المتصفح يدوياً على: http://localhost:8000
   - يجب أن ترى رسالة ترحيب

───────────────────────────────────────────────────────────────

🔍 للتشخيص:

   شغل: py check.py
   وأرسل النتائج

───────────────────────────────────────────────────────────────

📁 الملفات المهمة:

   تشغيل.bat           - تشغيل تلقائي (Windows)
   تشغيل_مباشر.py       - تشغيل مباشر
   check.py            - فحص النظام
   
   (تجاهل باقي الملفات)

───────────────────────────────────────────────────────────────

💬 للمساعدة:

   أرسل لي:
   1. نوع نظام التشغيل
   2. نتائج: py check.py
   3. أي رسائل خطأ تظهر
   4. لقطة شاشة إذا أمكن

═══════════════════════════════════════════════════════════════
