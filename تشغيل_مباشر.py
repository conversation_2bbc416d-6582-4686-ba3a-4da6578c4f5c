#!/usr/bin/env python3
"""
تشغيل مباشر للخادم - أبسط طريقة ممكنة
"""
import http.server
import socketserver
import json
import sys
import webbrowser
import threading
import time

def open_browser():
    """فتح المتصفح بعد ثانيتين"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:8000')
    except:
        pass

class Handler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.end_headers()
        
        response = {
            "message": "🏥 برنامج شعبة الأحياء - قسم الطبابة العدلية",
            "status": "✅ يعمل بنجاح",
            "version": "1.0.0",
            "info": "الخادم يعمل بشكل صحيح",
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}"
        }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False, indent=2).encode('utf-8'))
    
    def log_message(self, format, *args):
        pass

def main():
    PORT = 8000
    
    print("🏥 برنامج شعبة الأحياء - قسم الطبابة العدلية")
    print("=" * 55)
    print("🚀 بدء تشغيل الخادم...")
    
    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print(f"✅ الخادم يعمل على: http://localhost:{PORT}")
            print("🌐 سيتم فتح المتصفح تلقائياً...")
            print("🛑 اضغط Ctrl+C لإيقاف الخادم")
            print("=" * 55)
            
            # فتح المتصفح
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            httpd.serve_forever()
            
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ المنفذ {PORT} مستخدم")
            print("💡 انتظر دقيقة وأعد المحاولة")
        else:
            print(f"❌ خطأ: {e}")
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
