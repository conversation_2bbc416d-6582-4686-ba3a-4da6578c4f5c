"""
مسارات فحص الشدة
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from database import get_db
from models import IntensityExamCase, User
from routes.auth import get_current_user

router = APIRouter()

class IntensityExamCaseCreate(BaseModel):
    technical_number: str
    sending_authority: str
    patient_name: str
    patient_age: Optional[int] = None
    patient_gender: Optional[str] = None
    exam_date: datetime
    exam_results: Optional[str] = None
    medical_notes: Optional[str] = None

class IntensityExamCaseResponse(BaseModel):
    id: int
    technical_number: str
    sending_authority: str
    patient_name: str
    patient_age: Optional[int]
    patient_gender: Optional[str]
    exam_date: datetime
    exam_results: Optional[str]
    medical_notes: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True

@router.get("/", response_model=List[IntensityExamCaseResponse])
async def get_intensity_exam_cases(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """الحصول على قائمة حالات فحص الشدة"""
    cases = db.query(IntensityExamCase).offset(skip).limit(limit).all()
    return cases

@router.post("/", response_model=IntensityExamCaseResponse)
async def create_intensity_exam_case(
    case_data: IntensityExamCaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """إنشاء حالة فحص شدة جديدة"""
    new_case = IntensityExamCase(
        **case_data.dict(),
        created_by=current_user.id
    )
    
    db.add(new_case)
    db.commit()
    db.refresh(new_case)
    
    return new_case
