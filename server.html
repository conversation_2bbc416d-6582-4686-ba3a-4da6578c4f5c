<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج شعبة الأحياء - قسم الطبابة العدلية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 18px;
            margin-bottom: 30px;
        }
        
        .status {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .success {
            color: #4caf50;
            font-size: 18px;
            font-weight: bold;
        }
        
        .info {
            background: #f0f8ff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            border-right: 4px solid #667eea;
        }
        
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">ش.أ</div>
        
        <h1>برنامج شعبة الأحياء</h1>
        <div class="subtitle">قسم الطبابة العدلية</div>
        
        <div class="status">
            <div class="success">✅ البرنامج يعمل بنجاح!</div>
            <div>الإصدار 1.0.0</div>
        </div>
        
        <div class="info">
            <h3>معلومات النظام:</h3>
            <p><strong>نوع الخادم:</strong> HTML Static Page</p>
            <p><strong>الحالة:</strong> نشط</p>
            <p><strong>التاريخ:</strong> <span id="currentDate"></span></p>
            <p><strong>الوقت:</strong> <span id="currentTime"></span></p>
        </div>
        
        <div class="features">
            <div class="feature">
                <h4>تقدير الأعمار</h4>
                <p>إدارة حالات تقدير أعمار الأطفال</p>
            </div>
            
            <div class="feature">
                <h4>فحص الشدة</h4>
                <p>تسجيل ومتابعة حالات فحص الشدة</p>
            </div>
            
            <div class="feature">
                <h4>تحديد الجنس</h4>
                <p>إدارة حالات تحديد الجنس الطبي</p>
            </div>
            
            <div class="feature">
                <h4>التقارير</h4>
                <p>توليد التقارير والإحصائيات</p>
            </div>
        </div>
        
        <div class="note">
            <strong>ملاحظة:</strong> هذه نسخة تجريبية تعمل بدون Python. 
            للحصول على النسخة الكاملة مع قاعدة البيانات، يرجى حل مشكلة تثبيت Python.
        </div>
        
        <div class="info">
            <h3>بيانات تسجيل الدخول التجريبية:</h3>
            <p><strong>المدير:</strong> admin / admin123</p>
            <p><strong>المستخدم:</strong> user / user123</p>
        </div>
    </div>
    
    <script>
        function updateDateTime() {
            const now = new Date();
            document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-SA');
            document.getElementById('currentTime').textContent = now.toLocaleTimeString('ar-SA');
        }
        
        updateDateTime();
        setInterval(updateDateTime, 1000);
        
        // Show welcome message
        setTimeout(() => {
            alert('مرحباً بكم في برنامج شعبة الأحياء!\n\nهذه نسخة تجريبية تعمل بدون Python.');
        }, 1000);
    </script>
</body>
</html>
